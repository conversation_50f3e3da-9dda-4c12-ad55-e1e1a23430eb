/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/Project.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/exception/UnauthorizedException.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/repository/ActivityLogRepository.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/impl/ProjectServiceImpl.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/UserController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/TaskService.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/ApprovalDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/ProjectService.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/Approval.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/ActivityLogController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/UserRegistrationRequest.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/ApiResponse.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/PageResponse.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ProjectStatus.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/JwtResponse.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/ProjectDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ProjectMember.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/impl/ApprovalServiceImpl.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/DashboardStatsDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/TaskController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/config/DataInitializer.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/impl/DashboardServiceImpl.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/repository/TaskRepository.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/ProjectManagementApplication.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ApprovalStatus.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ActivityType.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/config/CorsConfig.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/repository/UserRepository.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/util/SecurityUtils.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/ActivityLogDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/ProjectController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ProjectRole.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/UserDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/repository/ApprovalRepository.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/ApprovalService.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/DashboardService.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/impl/TaskServiceImpl.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/ApprovalController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/ProjectMemberDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/exception/ValidationException.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/exception/UserAlreadyExistsException.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/impl/UserServiceImpl.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/exception/ResourceNotFoundException.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/Task.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/UserService.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/repository/ProjectMemberRepository.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/repository/ProjectRepository.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ApprovalType.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/DashboardController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/AuthController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/TaskDTO.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/dto/LoginRequest.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/User.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/exception/GlobalExceptionHandler.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/service/ActivityLogService.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/controller/TestController.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/TaskPriority.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/ActivityLog.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/TaskStatus.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/config/SecurityConfig.java
/Users/<USER>/ai/fs2/src/main/java/com/example/projectmanagement/entity/UserRole.java
