// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('登录功能测试', () => {
  const testUser = {
    username: `e2<PERSON>er${Date.now()}`,
    email: `e2etest${Date.now()}@example.com`,
    password: 'testpassword123',
    fullName: 'E2E测试用户'
  };

  test.beforeEach(async ({ request }) => {
    // 在每个测试前创建测试用户
    await request.post('http://localhost:8080/api/auth/register', {
      data: {
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        confirmPassword: testUser.password,
        fullName: testUser.fullName
      }
    });
  });

  test('用户应该能够成功登录', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    // 填写登录表单
    const usernameField = page.locator('input[name="username"], #username').first();
    const passwordField = page.locator('input[name="password"], #password').first();
    const submitButton = page.locator('button[type="submit"], .btn-login').first();
    
    if (await usernameField.isVisible()) {
      await usernameField.fill(testUser.username);
      await passwordField.fill(testUser.password);
      
      // 截图登录前
      await page.screenshot({ path: 'screenshots/before-login.png' });
      
      await submitButton.click();
      
      // 等待登录处理
      await page.waitForTimeout(2000);
      
      // 截图登录后
      await page.screenshot({ path: 'screenshots/after-login.png' });
      
      // 检查是否登录成功（这里需要根据实际的前端实现来调整）
      // 可能的成功指标：
      // 1. 页面跳转到主页或仪表板
      // 2. 显示用户信息
      // 3. 显示成功消息
      // 4. 隐藏登录表单
      
      // 检查URL是否改变或页面内容是否更新
      const currentUrl = page.url();
      console.log('登录后URL:', currentUrl);
    }
  });

  test('错误的用户名应该显示错误信息', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    const usernameField = page.locator('input[name="username"], #username').first();
    const passwordField = page.locator('input[name="password"], #password').first();
    const submitButton = page.locator('button[type="submit"], .btn-login').first();
    
    if (await usernameField.isVisible()) {
      await usernameField.fill('wrongusername');
      await passwordField.fill(testUser.password);
      await submitButton.click();
      
      // 等待错误处理
      await page.waitForTimeout(2000);
      
      // 检查是否显示错误信息
      const errorMessage = page.locator('.error, .alert-danger, .invalid-feedback');
      // await expect(errorMessage.first()).toBeVisible();
      
      await page.screenshot({ path: 'screenshots/login-error.png' });
    }
  });

  test('空表单提交应该显示验证错误', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    const submitButton = page.locator('button[type="submit"], .btn-login').first();
    
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // 等待验证处理
      await page.waitForTimeout(1000);
      
      // 检查是否显示验证错误
      const validationErrors = page.locator('.error, .invalid-feedback, .alert-danger');
      // 根据实际实现来验证
      
      await page.screenshot({ path: 'screenshots/validation-error.png' });
    }
  });

  test('登录表单应该支持键盘操作', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    const usernameField = page.locator('input[name="username"], #username').first();
    const passwordField = page.locator('input[name="password"], #password').first();
    
    if (await usernameField.isVisible()) {
      // 使用Tab键在字段间导航
      await usernameField.focus();
      await page.keyboard.type(testUser.username);
      await page.keyboard.press('Tab');
      await page.keyboard.type(testUser.password);
      
      // 使用Enter键提交表单
      await page.keyboard.press('Enter');
      
      // 等待处理
      await page.waitForTimeout(2000);
      
      await page.screenshot({ path: 'screenshots/keyboard-login.png' });
    }
  });

  test('密码字段应该隐藏输入内容', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    const passwordField = page.locator('input[name="password"], #password').first();
    
    if (await passwordField.isVisible()) {
      // 检查密码字段类型
      const inputType = await passwordField.getAttribute('type');
      expect(inputType).toBe('password');
      
      await passwordField.fill('secretpassword');
      
      // 检查输入的值是否被隐藏（在页面上不可见）
      await page.screenshot({ path: 'screenshots/password-hidden.png' });
    }
  });

  test('记住我功能应该正常工作', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    // 查找"记住我"复选框
    const rememberCheckbox = page.locator('input[type="checkbox"][name="remember"], #remember');
    
    if (await rememberCheckbox.isVisible()) {
      // 勾选"记住我"
      await rememberCheckbox.check();
      expect(await rememberCheckbox.isChecked()).toBe(true);
      
      // 取消勾选
      await rememberCheckbox.uncheck();
      expect(await rememberCheckbox.isChecked()).toBe(false);
      
      await page.screenshot({ path: 'screenshots/remember-me.png' });
    }
  });
});
