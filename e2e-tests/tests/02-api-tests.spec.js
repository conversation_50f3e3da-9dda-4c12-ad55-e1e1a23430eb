// @ts-check
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080';

test.describe('API接口测试', () => {
  test('健康检查接口应该正常', async ({ request }) => {
    const response = await request.get(`${API_BASE}/actuator/health`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.status).toBe('UP');
  });

  test('测试ping接口应该正常', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/test/ping`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBe('pong');
  });

  test('服务器状态接口应该正常', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/test/status`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.server).toContain('项目管理系统');
    expect(data.data.status).toBe('running');
  });

  test('测试项目列表接口应该正常', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/test/projects`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
    expect(data.data.length).toBeGreaterThan(0);
  });
});

test.describe('用户认证API测试', () => {
  let testUser = {
    username: `testuser${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    password: 'password123',
    fullName: '测试用户'
  };

  test('用户注册应该成功', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/auth/register`, {
      data: {
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        confirmPassword: testUser.password,
        fullName: testUser.fullName
      }
    });
    
    expect(response.status()).toBe(201);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.username).toBe(testUser.username);
    expect(data.data.email).toBe(testUser.email);
  });

  test('用户登录应该成功', async ({ request }) => {
    // 先注册用户
    await request.post(`${API_BASE}/api/auth/register`, {
      data: {
        username: testUser.username + '2',
        email: testUser.email.replace('@', '2@'),
        password: testUser.password,
        confirmPassword: testUser.password,
        fullName: testUser.fullName
      }
    });

    // 然后登录
    const response = await request.post(`${API_BASE}/api/auth/login`, {
      data: {
        username: testUser.username + '2',
        password: testUser.password
      }
    });
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.accessToken).toBeDefined();
    expect(data.data.username).toBe(testUser.username + '2');
  });

  test('错误的登录凭据应该失败', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/auth/login`, {
      data: {
        username: 'nonexistentuser',
        password: 'wrongpassword'
      }
    });
    
    expect(response.status()).toBe(401);
  });
});
