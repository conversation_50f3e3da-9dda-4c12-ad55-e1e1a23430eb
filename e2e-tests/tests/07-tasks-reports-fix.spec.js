// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('任务和报告页面初始化修复', () => {
  test('任务页面应该正常加载', async ({ page }) => {
    // 监听控制台消息
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text()
      });
    });

    // 监听页面错误
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });

    await page.goto('/pages/tasks.html');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 等待JavaScript执行
    await page.waitForTimeout(3000);
    
    // 检查是否有"应用初始化失败"或"初始化失败"的错误提示
    const errorDiv = page.locator('div:has-text("初始化失败"), div:has-text("应用初始化失败")');
    const errorCount = await errorDiv.count();
    
    if (errorCount > 0) {
      const errorText = await errorDiv.textContent();
      console.log('发现错误提示:', errorText);
    }
    
    // 记录控制台消息和错误
    console.log('=== 任务页面控制台消息 ===');
    consoleMessages.forEach(msg => {
      console.log(`[${msg.type}] ${msg.text}`);
    });
    
    if (pageErrors.length > 0) {
      console.log('=== 任务页面错误 ===');
      pageErrors.forEach(error => {
        console.log(`[ERROR] ${error}`);
      });
    }
    
    // 截图
    await page.screenshot({ path: 'screenshots/tasks-page-debug.png' });
    
    // 断言：不应该有初始化失败的错误
    expect(errorCount).toBe(0);
    
    // 检查页面基本元素
    await expect(page.locator('body')).toBeVisible();
    
    console.log('✅ 任务页面测试完成');
  });

  test('报告页面应该正常加载', async ({ page }) => {
    // 监听控制台消息
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text()
      });
    });

    // 监听页面错误
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });

    await page.goto('/pages/reports.html');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 等待JavaScript执行
    await page.waitForTimeout(3000);
    
    // 检查是否有错误提示
    const errorDiv = page.locator('div:has-text("初始化失败"), div:has-text("应用初始化失败")');
    const errorCount = await errorDiv.count();
    
    if (errorCount > 0) {
      const errorText = await errorDiv.textContent();
      console.log('发现错误提示:', errorText);
    }
    
    // 记录控制台消息和错误
    console.log('=== 报告页面控制台消息 ===');
    consoleMessages.forEach(msg => {
      console.log(`[${msg.type}] ${msg.text}`);
    });
    
    if (pageErrors.length > 0) {
      console.log('=== 报告页面错误 ===');
      pageErrors.forEach(error => {
        console.log(`[ERROR] ${error}`);
      });
    }
    
    // 截图
    await page.screenshot({ path: 'screenshots/reports-page-debug.png' });
    
    // 断言：不应该有初始化失败的错误
    expect(errorCount).toBe(0);
    
    // 检查页面基本元素
    await expect(page.locator('body')).toBeVisible();
    
    console.log('✅ 报告页面测试完成');
  });

  test('检查API类是否正确定义', async ({ page }) => {
    await page.goto('/pages/tasks.html');
    await page.waitForLoadState('networkidle');
    
    // 检查各种API类是否定义
    const apiStatus = await page.evaluate(() => {
      return {
        TaskAPI: typeof TaskAPI !== 'undefined',
        ProjectAPI: typeof ProjectAPI !== 'undefined',
        UserAPI: typeof UserAPI !== 'undefined',
        AuthAPI: typeof AuthAPI !== 'undefined',
        APP_CONFIG: typeof APP_CONFIG !== 'undefined'
      };
    });
    
    console.log('API类定义状态:', apiStatus);
    
    expect(apiStatus.TaskAPI).toBe(true);
    expect(apiStatus.ProjectAPI).toBe(true);
    expect(apiStatus.AuthAPI).toBe(true);
    expect(apiStatus.APP_CONFIG).toBe(true);
  });
});
