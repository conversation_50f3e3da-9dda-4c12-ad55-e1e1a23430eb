// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('管理员账号登录测试', () => {
  const adminCredentials = {
    username: 'admin',
    password: '123456'
  };

  test('管理员应该能够成功登录', async ({ page }) => {
    await page.goto('/pages/login.html');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 填写管理员登录信息
    const usernameField = page.locator('input[name="username"], #username').first();
    const passwordField = page.locator('input[name="password"], #password').first();
    const submitButton = page.locator('button[type="submit"], .btn-login').first();
    
    if (await usernameField.isVisible()) {
      await usernameField.fill(adminCredentials.username);
      await passwordField.fill(adminCredentials.password);
      
      // 截图登录前
      await page.screenshot({ path: 'screenshots/admin-login-before.png' });
      
      await submitButton.click();
      
      // 等待登录处理
      await page.waitForTimeout(3000);
      
      // 截图登录后
      await page.screenshot({ path: 'screenshots/admin-login-after.png' });
      
      // 检查是否登录成功
      const currentUrl = page.url();
      console.log('登录后URL:', currentUrl);
      
      // 检查页面内容是否有变化
      const pageContent = await page.locator('body').textContent();
      console.log('页面内容包含admin:', pageContent.includes('admin'));
      
      // 基本断言 - 页面应该正常显示
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('管理员登录API应该返回正确的角色信息', async ({ request }) => {
    const response = await request.post('http://localhost:8080/api/auth/login', {
      data: {
        username: adminCredentials.username,
        password: adminCredentials.password
      }
    });
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.username).toBe('admin');
    expect(data.data.role).toBe('ADMIN');
    expect(data.data.userId).toBe(1);
    expect(data.data.accessToken).toBeDefined();
    
    console.log('管理员登录响应:', JSON.stringify(data, null, 2));
  });

  test('错误的管理员密码应该被拒绝', async ({ request }) => {
    const response = await request.post('http://localhost:8080/api/auth/login', {
      data: {
        username: adminCredentials.username,
        password: 'wrongpassword'
      }
    });

    // 接受401或200状态码，因为不同的实现可能返回不同的状态码
    expect([200, 401]).toContain(response.status());

    if (response.status() === 200) {
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.message).toContain('用户名或密码错误');
    }
  });

  test('旧的管理员密码应该被拒绝', async ({ request }) => {
    const response = await request.post('http://localhost:8080/api/auth/login', {
      data: {
        username: adminCredentials.username,
        password: 'password123'
      }
    });

    // 接受401或200状态码，因为不同的实现可能返回不同的状态码
    expect([200, 401]).toContain(response.status());

    if (response.status() === 200) {
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.message).toContain('用户名或密码错误');
    }
  });

  test('管理员账号信息应该正确存储在数据库中', async ({ request }) => {
    // 首先登录获取token
    const loginResponse = await request.post('http://localhost:8080/api/auth/login', {
      data: {
        username: adminCredentials.username,
        password: adminCredentials.password
      }
    });
    
    const loginData = await loginResponse.json();
    expect(loginData.success).toBe(true);
    
    // 验证用户信息
    const userInfo = loginData.data;
    expect(userInfo.username).toBe('admin');
    expect(userInfo.role).toBe('ADMIN');
    expect(userInfo.userId).toBe(1);
    
    console.log('管理员用户信息验证通过:', {
      id: userInfo.userId,
      username: userInfo.username,
      role: userInfo.role
    });
  });

  test('其他测试用户应该仍然可以正常登录', async ({ request }) => {
    // 测试项目经理账号
    const managerResponse = await request.post('http://localhost:8080/api/auth/login', {
      data: {
        username: 'manager',
        password: 'password123'
      }
    });
    
    expect(managerResponse.status()).toBe(200);
    const managerData = await managerResponse.json();
    expect(managerData.success).toBe(true);
    expect(managerData.data.role).toBe('MANAGER');
    
    // 测试普通用户账号
    const userResponse = await request.post('http://localhost:8080/api/auth/login', {
      data: {
        username: 'user1',
        password: 'password123'
      }
    });
    
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    expect(userData.success).toBe(true);
    expect(userData.data.role).toBe('MEMBER');
    
    console.log('其他测试用户验证通过');
  });
});
