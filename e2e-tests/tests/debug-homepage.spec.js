// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('主页调试测试', () => {
  test('检查主页元素', async ({ page }) => {
    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 截图查看页面状态
    await page.screenshot({ path: 'screenshots/debug-homepage.png', fullPage: true });
    
    // 检查页面标题
    const title = await page.title();
    console.log('页面标题:', title);
    
    // 检查页面HTML内容
    const bodyContent = await page.locator('body').innerHTML();
    console.log('页面内容长度:', bodyContent.length);
    
    // 检查是否有导航栏
    const navbar = page.locator('nav, .navbar');
    const navbarCount = await navbar.count();
    console.log('导航栏数量:', navbarCount);
    
    if (navbarCount > 0) {
      const navbarText = await navbar.first().textContent();
      console.log('导航栏文本:', navbarText);
    }
    
    // 检查navbar-brand元素
    const navbarBrand = page.locator('.navbar-brand');
    const brandCount = await navbarBrand.count();
    console.log('navbar-brand元素数量:', brandCount);
    
    if (brandCount > 0) {
      const brandText = await navbarBrand.textContent();
      console.log('navbar-brand文本:', brandText);
      await expect(navbarBrand).toContainText('项目管理系统');
    } else {
      console.log('未找到.navbar-brand元素');
      
      // 检查所有包含"项目管理系统"的元素
      const allElements = await page.locator('*:has-text("项目管理系统")').all();
      console.log('包含"项目管理系统"的元素数量:', allElements.length);
      
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        const tagName = await element.evaluate(el => el.tagName);
        const className = await element.getAttribute('class');
        const text = await element.textContent();
        console.log(`元素${i + 1}: ${tagName}, class="${className}", text="${text}"`);
      }
    }
    
    // 检查CSS是否加载
    const stylesheets = await page.evaluate(() => {
      return Array.from(document.styleSheets).map(sheet => {
        try {
          return {
            href: sheet.href,
            rules: sheet.cssRules ? sheet.cssRules.length : 0
          };
        } catch (e) {
          return { href: sheet.href, error: e.message };
        }
      });
    });
    console.log('样式表:', JSON.stringify(stylesheets, null, 2));
    
    // 检查控制台错误
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // 等待一下看是否有错误
    await page.waitForTimeout(1000);
    
    if (errors.length > 0) {
      console.log('控制台错误:', errors);
    }
    
    // 基本断言 - 页面应该加载
    await expect(page.locator('body')).toBeVisible();
  });
});
