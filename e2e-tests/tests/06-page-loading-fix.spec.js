// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('页面加载修复验证', () => {
  test('主页应该正常加载，不显示初始化失败错误', async ({ page }) => {
    // 监听控制台消息
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text()
      });
    });

    // 监听页面错误
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });

    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 等待一下看JavaScript是否执行完成
    await page.waitForTimeout(3000);
    
    // 检查是否有"应用初始化失败"的错误提示
    const errorDiv = page.locator('div:has-text("应用初始化失败")');
    const errorCount = await errorDiv.count();
    
    if (errorCount > 0) {
      const errorText = await errorDiv.textContent();
      console.log('发现错误提示:', errorText);
    }
    
    // 断言：不应该有初始化失败的错误
    expect(errorCount).toBe(0);
    
    // 检查控制台消息
    console.log('控制台消息:');
    consoleMessages.forEach(msg => {
      console.log(`[${msg.type}] ${msg.text}`);
    });
    
    // 检查页面错误
    if (pageErrors.length > 0) {
      console.log('页面错误:');
      pageErrors.forEach(error => {
        console.log(`[ERROR] ${error}`);
      });
    }
    
    // 断言：不应该有严重的页面错误
    const criticalErrors = pageErrors.filter(error => 
      error.includes('ReferenceError') || 
      error.includes('TypeError') || 
      error.includes('SyntaxError')
    );
    expect(criticalErrors.length).toBe(0);
    
    // 检查页面基本元素是否存在
    await expect(page.locator('body')).toBeVisible();
    
    // 截图
    await page.screenshot({ path: 'screenshots/page-loading-fix.png' });
    
    console.log('✅ 页面加载测试完成');
  });

  test('登录页面应该正常加载', async ({ page }) => {
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text()
      });
    });

    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });

    await page.goto('/pages/login.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // 检查是否有错误提示
    const errorDiv = page.locator('div:has-text("应用初始化失败")');
    const errorCount = await errorDiv.count();
    expect(errorCount).toBe(0);
    
    // 检查登录表单是否存在
    const usernameField = page.locator('input[name="username"], #username');
    const passwordField = page.locator('input[name="password"], #password');
    
    await expect(usernameField.first()).toBeVisible();
    await expect(passwordField.first()).toBeVisible();
    
    // 截图
    await page.screenshot({ path: 'screenshots/login-page-fix.png' });
    
    console.log('✅ 登录页面加载测试完成');
  });

  test('JavaScript配置应该正确加载', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 检查APP_CONFIG是否正确定义
    const appConfig = await page.evaluate(() => {
      return typeof APP_CONFIG !== 'undefined' ? APP_CONFIG : null;
    });
    
    expect(appConfig).not.toBeNull();
    expect(appConfig.API_BASE_URL).toBeDefined();
    expect(appConfig.API_TIMEOUT).toBeDefined();
    
    console.log('APP_CONFIG:', appConfig);
    
    // 检查AuthAPI是否正确定义
    const authAPIExists = await page.evaluate(() => {
      return typeof AuthAPI !== 'undefined';
    });
    
    expect(authAPIExists).toBe(true);
    
    console.log('✅ JavaScript配置检查完成');
  });
});
