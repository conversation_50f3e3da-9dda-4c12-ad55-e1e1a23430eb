{"config": {"configFile": "/Users/<USER>/ai/fs2/e2e-tests/playwright.config.js", "rootDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/ai/fs2/e2e-tests/global-setup.js", "globalTeardown": "/Users/<USER>/ai/fs2/e2e-tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": null}, "suites": [{"title": "03-user-interactions.spec.js", "file": "03-user-interactions.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "用户交互测试", "file": "03-user-interactions.spec.js", "line": 4, "column": 6, "specs": [{"title": "导航菜单应该正常工作", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5053, "errors": [], "stdout": [{"text": "当前页面标题: 登录 - 项目管理系统\n"}, {"text": "页面重定向到登录页，这是正常的\n"}, {"text": "找到品牌元素数量: \u001b[33m2\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:52:20.353Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "19b9ca4f6f7b965437f0-fec9e1f7adf4aff85beb", "file": "03-user-interactions.spec.js", "line": 5, "column": 3}, {"title": "响应式设计应该正常工作", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6607, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:52:20.360Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "19b9ca4f6f7b965437f0-fcb365e5939cfbc82f85", "file": "03-user-interactions.spec.js", "line": 63, "column": 3}, {"title": "表单验证应该正常工作", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6462, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:52:20.356Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "19b9ca4f6f7b965437f0-cdc37e9b8b92ad963615", "file": "03-user-interactions.spec.js", "line": 82, "column": 3}, {"title": "页面加载性能应该合理", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 8492, "errors": [], "stdout": [{"text": "页面加载时间: 0.20000000298023224ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:52:20.349Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "19b9ca4f6f7b965437f0-0302faed31636ea252dd", "file": "03-user-interactions.spec.js", "line": 100, "column": 3}, {"title": "JavaScript错误应该被捕获", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 13209, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:52:25.577Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "19b9ca4f6f7b965437f0-39113fa25b5448751678", "file": "03-user-interactions.spec.js", "line": 119, "column": 3}, {"title": "链接应该正常工作", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6120, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:52:26.983Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "19b9ca4f6f7b965437f0-a7555d6347693b9aac32", "file": "03-user-interactions.spec.js", "line": 148, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T06:52:14.898Z", "duration": 23911.089, "expected": 6, "skipped": 0, "unexpected": 0, "flaky": 0}}