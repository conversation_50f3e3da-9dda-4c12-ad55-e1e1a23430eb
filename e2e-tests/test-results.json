{"config": {"configFile": "/Users/<USER>/ai/fs2/e2e-tests/playwright.config.js", "rootDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/ai/fs2/e2e-tests/global-setup.js", "globalTeardown": "/Users/<USER>/ai/fs2/e2e-tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 3}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": null}, "suites": [{"title": "06-page-loading-fix.spec.js", "file": "06-page-loading-fix.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "页面加载修复验证", "file": "06-page-loading-fix.spec.js", "line": 4, "column": 6, "specs": [{"title": "主页应该正常加载，不显示初始化失败错误", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 10296, "errors": [], "stdout": [{"text": "控制台消息:\n"}, {"text": "[log] 应用配置已加载: {API_BASE_URL: http://localhost:8080/api, API_TIMEOUT: 30000, TOKEN_KEY: pm_access_token, REFRESH_TOKEN_KEY: pm_refresh_token, USER_KEY: pm_user_info}\n"}, {"text": "[log] 当前环境: 开发环境\n"}, {"text": "[log] 主JavaScript文件加载完成\n"}, {"text": "[log] 应用初始化完成\n"}, {"text": "✅ 页面加载测试完成\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:23:12.972Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "6d1f9f77bf9951977aaf-9a92ac7a2c2e3342ed61", "file": "06-page-loading-fix.spec.js", "line": 5, "column": 3}, {"title": "登录页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 9335, "errors": [], "stdout": [{"text": "✅ 登录页面加载测试完成\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:23:12.973Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "6d1f9f77bf9951977aaf-2ad1ab70e343927baaa3", "file": "06-page-loading-fix.spec.js", "line": 72, "column": 3}, {"title": "JavaScript配置应该正确加载", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 10853, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeDefined\u001b[2m()\u001b[22m\n\nReceived: \u001b[31mundefined\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeDefined\u001b[2m()\u001b[22m\n\nReceived: \u001b[31mundefined\u001b[39m\n    at /Users/<USER>/ai/fs2/e2e-tests/tests/06-page-loading-fix.spec.js:119:35", "location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/06-page-loading-fix.spec.js", "column": 35, "line": 119}, "snippet": "\u001b[0m \u001b[90m 117 |\u001b[39m     expect(appConfig)\u001b[33m.\u001b[39mnot\u001b[33m.\u001b[39mtoBeNull()\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m     expect(appConfig\u001b[33m.\u001b[39m\u001b[33mAPI_BASE_URL\u001b[39m)\u001b[33m.\u001b[39mtoBeDefined()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m     expect(appConfig\u001b[33m.\u001b[39m\u001b[33mAPI_TIMEOUT\u001b[39m)\u001b[33m.\u001b[39mtoBeDefined()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m     \n \u001b[90m 121 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'APP_CONFIG:'\u001b[39m\u001b[33m,\u001b[39m appConfig)\u001b[33m;\u001b[39m\n \u001b[90m 122 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/06-page-loading-fix.spec.js", "column": 35, "line": 119}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeDefined\u001b[2m()\u001b[22m\n\nReceived: \u001b[31mundefined\u001b[39m\n\n\u001b[0m \u001b[90m 117 |\u001b[39m     expect(appConfig)\u001b[33m.\u001b[39mnot\u001b[33m.\u001b[39mtoBeNull()\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m     expect(appConfig\u001b[33m.\u001b[39m\u001b[33mAPI_BASE_URL\u001b[39m)\u001b[33m.\u001b[39mtoBeDefined()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m     expect(appConfig\u001b[33m.\u001b[39m\u001b[33mAPI_TIMEOUT\u001b[39m)\u001b[33m.\u001b[39mtoBeDefined()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m     \n \u001b[90m 121 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'APP_CONFIG:'\u001b[39m\u001b[33m,\u001b[39m appConfig)\u001b[33m;\u001b[39m\n \u001b[90m 122 |\u001b[39m     \u001b[0m\n\u001b[2m    at /Users/<USER>/ai/fs2/e2e-tests/tests/06-page-loading-fix.spec.js:119:35\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:23:12.972Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/06-page-loading-fix.spec.js", "column": 35, "line": 119}}], "status": "unexpected"}], "id": "6d1f9f77bf9951977aaf-9e44749786793a4b33e9", "file": "06-page-loading-fix.spec.js", "line": 108, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T07:23:07.513Z", "duration": 16614.740999999998, "expected": 2, "skipped": 0, "unexpected": 1, "flaky": 0}}