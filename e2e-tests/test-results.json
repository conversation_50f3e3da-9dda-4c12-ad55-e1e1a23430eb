{"config": {"configFile": "/Users/<USER>/ai/fs2/e2e-tests/playwright.config.js", "rootDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/ai/fs2/e2e-tests/global-setup.js", "globalTeardown": "/Users/<USER>/ai/fs2/e2e-tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 3}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": null}, "suites": [{"title": "07-tasks-reports-fix.spec.js", "file": "07-tasks-reports-fix.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "任务和报告页面初始化修复", "file": "07-tasks-reports-fix.spec.js", "line": 4, "column": 6, "specs": [{"title": "任务页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6192, "errors": [], "stdout": [{"text": "=== 任务页面控制台消息 ===\n"}, {"text": "[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED\n"}, {"text": "[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED\n"}, {"text": "[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED\n"}, {"text": "[log] 应用配置已加载: {API_BASE_URL: http://localhost:8080/api, API_TIMEOUT: 30000, TOKEN_KEY: pm_access_token, REFRESH_TOKEN_KEY: pm_refresh_token, USER_KEY: pm_user_info}\n"}, {"text": "[log] 当前环境: 开发环境\n"}, {"text": "[log] 主JavaScript文件加载完成\n"}, {"text": "[warning] AuthAPI未定义，某些功能可能不可用\n"}, {"text": "[log] 应用初始化完成\n"}, {"text": "[error] Failed to load resource: the server responded with a status of 404 (File not found)\n"}, {"text": "=== 任务页面错误 ===\n"}, {"text": "[ERROR] TaskAPI is not defined\n"}, {"text": "✅ 任务页面测试完成\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:34:46.362Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "49b162330c29e18b334f-33a4f1543186492f1299", "file": "07-tasks-reports-fix.spec.js", "line": 5, "column": 3}, {"title": "报告页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6285, "errors": [], "stdout": [{"text": "=== 报告页面控制台消息 ===\n"}, {"text": "[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED\n"}, {"text": "[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED\n"}, {"text": "[error] Failed to load resource: net::ERR_CONNECTION_RESET\n"}, {"text": "[log] 应用配置已加载: {API_BASE_URL: http://localhost:8080/api, API_TIMEOUT: 30000, TOKEN_KEY: pm_access_token, REFRESH_TOKEN_KEY: pm_refresh_token, USER_KEY: pm_user_info}\n"}, {"text": "[log] 当前环境: 开发环境\n"}, {"text": "[log] 主JavaScript文件加载完成\n"}, {"text": "[log] 应用初始化完成\n"}, {"text": "[error] Failed to load resource: the server responded with a status of 404 (File not found)\n"}, {"text": "✅ 报告页面测试完成\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:34:46.364Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "49b162330c29e18b334f-a6059637b938617e8737", "file": "07-tasks-reports-fix.spec.js", "line": 63, "column": 3}, {"title": "检查API类是否正确定义", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 3423, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/ai/fs2/e2e-tests/tests/07-tasks-reports-fix.spec.js:138:31", "location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/07-tasks-reports-fix.spec.js", "column": 31, "line": 138}, "snippet": "\u001b[0m \u001b[90m 136 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'API类定义状态:'\u001b[39m\u001b[33m,\u001b[39m apiStatus)\u001b[33m;\u001b[39m\n \u001b[90m 137 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 138 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mTaskAPI\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 139 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mProjectAPI\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 140 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mAuthAPI\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 141 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mAPP_CONFIG\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/07-tasks-reports-fix.spec.js", "column": 31, "line": 138}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 136 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'API类定义状态:'\u001b[39m\u001b[33m,\u001b[39m apiStatus)\u001b[33m;\u001b[39m\n \u001b[90m 137 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 138 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mTaskAPI\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 139 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mProjectAPI\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 140 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mAuthAPI\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 141 |\u001b[39m     expect(apiStatus\u001b[33m.\u001b[39m\u001b[33mAPP_CONFIG\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/ai/fs2/e2e-tests/tests/07-tasks-reports-fix.spec.js:138:31\u001b[22m"}], "stdout": [{"text": "API类定义状态: {\n  TaskAPI: \u001b[33mfalse\u001b[39m,\n  ProjectAPI: \u001b[33mfalse\u001b[39m,\n  UserAPI: \u001b[33mfalse\u001b[39m,\n  AuthAPI: \u001b[33mfalse\u001b[39m,\n  APP_CONFIG: \u001b[33mfalse\u001b[39m\n}\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:34:46.361Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/07-tasks-reports-fix.spec.js", "column": 31, "line": 138}}], "status": "unexpected"}], "id": "49b162330c29e18b334f-df83c89036df3243e244", "file": "07-tasks-reports-fix.spec.js", "line": 121, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T07:34:40.933Z", "duration": 12029.245, "expected": 2, "skipped": 0, "unexpected": 1, "flaky": 0}}