{"config": {"configFile": "/Users/<USER>/ai/fs2/e2e-tests/playwright.config.js", "rootDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/ai/fs2/e2e-tests/global-setup.js", "globalTeardown": "/Users/<USER>/ai/fs2/e2e-tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": null}, "suites": [{"title": "01-basic-pages.spec.js", "file": "01-basic-pages.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "基础页面测试", "file": "01-basic-pages.spec.js", "line": 4, "column": 6, "specs": [{"title": "主页应该正常加载（未登录时重定向到登录页）", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5350, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:31.203Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "751751f73c873d7d6fac-f5b1a89f547f97df2c36", "file": "01-basic-pages.spec.js", "line": 5, "column": 3}, {"title": "登录页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6892, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:31.206Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "751751f73c873d7d6fac-def5b5000050dc2d83c6", "file": "01-basic-pages.spec.js", "line": 32, "column": 3}, {"title": "项目页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 4328, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:31.201Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "751751f73c873d7d6fac-2b1759734c0910975969", "file": "01-basic-pages.spec.js", "line": 47, "column": 3}, {"title": "任务页面应该正常加载", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 8378, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/任务|项目管理系统/\u001b[39m\nReceived string:  \u001b[31m\"Error response\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 5000ms\u001b[22m\n\u001b[2m    9 × unexpected value \"Error response\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/任务|项目管理系统/\u001b[39m\nReceived string:  \u001b[31m\"Error response\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 5000ms\u001b[22m\n\u001b[2m    9 × unexpected value \"Error response\"\u001b[22m\n\n    at /Users/<USER>/ai/fs2/e2e-tests/tests/01-basic-pages.spec.js:64:24", "location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/01-basic-pages.spec.js", "column": 24, "line": 64}, "snippet": "\u001b[0m \u001b[90m 62 |\u001b[39m     \n \u001b[90m 63 |\u001b[39m     \u001b[90m// 检查页面标题\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/任务|项目管理系统/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 65 |\u001b[39m     \n \u001b[90m 66 |\u001b[39m     \u001b[90m// 检查页面主要内容\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'body'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/01-basic-pages.spec.js", "column": 24, "line": 64}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/任务|项目管理系统/\u001b[39m\nReceived string:  \u001b[31m\"Error response\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 5000ms\u001b[22m\n\u001b[2m    9 × unexpected value \"Error response\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 62 |\u001b[39m     \n \u001b[90m 63 |\u001b[39m     \u001b[90m// 检查页面标题\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/任务|项目管理系统/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 65 |\u001b[39m     \n \u001b[90m 66 |\u001b[39m     \u001b[90m// 检查页面主要内容\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'body'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/ai/fs2/e2e-tests/tests/01-basic-pages.spec.js:64:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:31.202Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/ai/fs2/e2e-tests/test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/01-basic-pages.spec.js", "column": 24, "line": 64}}], "status": "unexpected"}], "id": "751751f73c873d7d6fac-b504e4bd99a0e884f235", "file": "01-basic-pages.spec.js", "line": 60, "column": 3}, {"title": "用户页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 7165, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:35.891Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "751751f73c873d7d6fac-be6de7b48212aa93e37b", "file": "01-basic-pages.spec.js", "line": 73, "column": 3}, {"title": "审核页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5196, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:36.909Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "751751f73c873d7d6fac-966a9a9f0f94ed36597e", "file": "01-basic-pages.spec.js", "line": 86, "column": 3}, {"title": "报告页面应该正常加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5167, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T06:20:38.454Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "751751f73c873d7d6fac-2ac19d5bb14d1150a4b8", "file": "01-basic-pages.spec.js", "line": 99, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T06:20:25.734Z", "duration": 17910.559999999998, "expected": 6, "skipped": 0, "unexpected": 1, "flaky": 0}}