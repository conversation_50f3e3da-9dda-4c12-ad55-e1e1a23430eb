{"config": {"configFile": "/Users/<USER>/ai/fs2/e2e-tests/playwright.config.js", "rootDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/ai/fs2/e2e-tests/global-setup.js", "globalTeardown": "/Users/<USER>/ai/fs2/e2e-tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/ai/fs2/e2e-tests/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/ai/fs2/e2e-tests/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": null}, "suites": [{"title": "05-admin-login.spec.js", "file": "05-admin-login.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "管理员账号登录测试", "file": "05-admin-login.spec.js", "line": 4, "column": 6, "specs": [{"title": "管理员应该能够成功登录", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8515, "errors": [], "stdout": [{"text": "登录后URL: http://localhost:3000/pages/login.html\n"}, {"text": "页面内容包含admin: \u001b[33mfalse\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:12:11.478Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "48b2af809defcdbf221b-b0e7aa0481c75ec29911", "file": "05-admin-login.spec.js", "line": 10, "column": 3}, {"title": "管理员登录API应该返回正确的角色信息", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 140, "errors": [], "stdout": [{"text": "管理员登录响应: {\n  \"success\": true,\n  \"code\": \"200\",\n  \"message\": \"登录成功\",\n  \"data\": {\n    \"accessToken\": \"mock-jwt-token-1753341131608\",\n    \"tokenType\": \"Bearer\",\n    \"refreshToken\": null,\n    \"expiresIn\": 3600,\n    \"userId\": 1,\n    \"username\": \"admin\",\n    \"role\": \"ADMIN\"\n  }\n}\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:12:11.477Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "48b2af809defcdbf221b-32c5710d3eacfa51e19f", "file": "05-admin-login.spec.js", "line": 49, "column": 3}, {"title": "错误的管理员密码应该被拒绝", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 135, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m401\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m401\u001b[39m\n    at /Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js:77:31", "location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js", "column": 31, "line": 77}, "snippet": "\u001b[0m \u001b[90m 75 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 76 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 77 |\u001b[39m     expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 78 |\u001b[39m     \n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 80 |\u001b[39m     expect(data\u001b[33m.\u001b[39msuccess)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js", "column": 31, "line": 77}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m401\u001b[39m\n\n\u001b[0m \u001b[90m 75 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 76 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 77 |\u001b[39m     expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 78 |\u001b[39m     \n \u001b[90m 79 |\u001b[39m     \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 80 |\u001b[39m     expect(data\u001b[33m.\u001b[39msuccess)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js:77:31\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:12:11.477Z", "annotations": [], "attachments": [], "errorLocation": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js", "column": 31, "line": 77}}], "status": "unexpected"}], "id": "48b2af809defcdbf221b-ef85763865f411792f7a", "file": "05-admin-login.spec.js", "line": 69, "column": 3}, {"title": "旧的管理员密码应该被拒绝", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 135, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m401\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m401\u001b[39m\n    at /Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js:92:31", "location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js", "column": 31, "line": 92}, "snippet": "\u001b[0m \u001b[90m 90 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m     expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m     \n \u001b[90m 94 |\u001b[39m     \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m     expect(data\u001b[33m.\u001b[39msuccess)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js", "column": 31, "line": 92}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m401\u001b[39m\n\n\u001b[0m \u001b[90m 90 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 91 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m     expect(response\u001b[33m.\u001b[39mstatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m     \n \u001b[90m 94 |\u001b[39m     \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m     expect(data\u001b[33m.\u001b[39msuccess)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js:92:31\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:12:11.496Z", "annotations": [], "attachments": [], "errorLocation": {"file": "/Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js", "column": 31, "line": 92}}], "status": "unexpected"}], "id": "48b2af809defcdbf221b-6a59cd86d198f5a3fe55", "file": "05-admin-login.spec.js", "line": 84, "column": 3}, {"title": "管理员账号信息应该正确存储在数据库中", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 2, "status": "passed", "duration": 118, "errors": [], "stdout": [{"text": "管理员用户信息验证通过: { id: \u001b[33m1\u001b[39m, username: \u001b[32m'admin'\u001b[39m, role: \u001b[32m'ADMIN'\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:12:12.004Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "48b2af809defcdbf221b-c4b6f95a2b9561d725d0", "file": "05-admin-login.spec.js", "line": 99, "column": 3}, {"title": "其他测试用户应该仍然可以正常登录", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 237, "errors": [], "stdout": [{"text": "其他测试用户验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-24T07:12:11.625Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "48b2af809defcdbf221b-e3a885183676a36f9c3d", "file": "05-admin-login.spec.js", "line": 124, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-24T07:12:05.969Z", "duration": 14273.300000000001, "expected": 4, "skipped": 0, "unexpected": 2, "flaky": 0}}