<testsuites id="" name="" tests="3" failures="1" skipped="0" errors="0" time="12.029245000000001">
<testsuite name="07-tasks-reports-fix.spec.js" timestamp="2025-07-24T07:34:46.012Z" hostname="chromium" tests="3" failures="1" skipped="0" time="15.9" errors="0">
<testcase name="任务和报告页面初始化修复 › 任务页面应该正常加载" classname="07-tasks-reports-fix.spec.js" time="6.192">
<system-out>
<![CDATA[=== 任务页面控制台消息 ===
[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED
[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED
[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED
[log] 应用配置已加载: {API_BASE_URL: http://localhost:8080/api, API_TIMEOUT: 30000, TOKEN_KEY: pm_access_token, REFRESH_TOKEN_KEY: pm_refresh_token, USER_KEY: pm_user_info}
[log] 当前环境: 开发环境
[log] 主JavaScript文件加载完成
[warning] AuthAPI未定义，某些功能可能不可用
[log] 应用初始化完成
[error] Failed to load resource: the server responded with a status of 404 (File not found)
=== 任务页面错误 ===
[ERROR] TaskAPI is not defined
✅ 任务页面测试完成
]]>
</system-out>
</testcase>
<testcase name="任务和报告页面初始化修复 › 报告页面应该正常加载" classname="07-tasks-reports-fix.spec.js" time="6.285">
<system-out>
<![CDATA[=== 报告页面控制台消息 ===
[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED
[error] Failed to load resource: net::ERR_SOCKET_NOT_CONNECTED
[error] Failed to load resource: net::ERR_CONNECTION_RESET
[log] 应用配置已加载: {API_BASE_URL: http://localhost:8080/api, API_TIMEOUT: 30000, TOKEN_KEY: pm_access_token, REFRESH_TOKEN_KEY: pm_refresh_token, USER_KEY: pm_user_info}
[log] 当前环境: 开发环境
[log] 主JavaScript文件加载完成
[log] 应用初始化完成
[error] Failed to load resource: the server responded with a status of 404 (File not found)
✅ 报告页面测试完成
]]>
</system-out>
</testcase>
<testcase name="任务和报告页面初始化修复 › 检查API类是否正确定义" classname="07-tasks-reports-fix.spec.js" time="3.423">
<failure message="07-tasks-reports-fix.spec.js:121:3 检查API类是否正确定义" type="FAILURE">
<![CDATA[  [chromium] › 07-tasks-reports-fix.spec.js:121:3 › 任务和报告页面初始化修复 › 检查API类是否正确定义 ────────────────────

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      136 |     console.log('API类定义状态:', apiStatus);
      137 |     
    > 138 |     expect(apiStatus.TaskAPI).toBe(true);
          |                               ^
      139 |     expect(apiStatus.ProjectAPI).toBe(true);
      140 |     expect(apiStatus.AuthAPI).toBe(true);
      141 |     expect(apiStatus.APP_CONFIG).toBe(true);
        at /Users/<USER>/ai/fs2/e2e-tests/tests/07-tasks-reports-fix.spec.js:138:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[API类定义状态: {
  TaskAPI: [33mfalse[39m,
  ProjectAPI: [33mfalse[39m,
  UserAPI: [33mfalse[39m,
  AuthAPI: [33mfalse[39m,
  APP_CONFIG: [33mfalse[39m
}

[[ATTACHMENT|test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/video.webm]]

[[ATTACHMENT|test-results/07-tasks-reports-fix-任务和报告页面初始化修复-检查API类是否正确定义-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>