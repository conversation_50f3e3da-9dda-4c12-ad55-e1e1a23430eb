<testsuites id="" name="" tests="6" failures="2" skipped="0" errors="0" time="14.2733">
<testsuite name="05-admin-login.spec.js" timestamp="2025-07-24T07:12:11.041Z" hostname="chromium" tests="6" failures="2" skipped="0" time="9.28" errors="0">
<testcase name="管理员账号登录测试 › 管理员应该能够成功登录" classname="05-admin-login.spec.js" time="8.515">
<system-out>
<![CDATA[登录后URL: http://localhost:3000/pages/login.html
页面内容包含admin: [33mfalse[39m
]]>
</system-out>
</testcase>
<testcase name="管理员账号登录测试 › 管理员登录API应该返回正确的角色信息" classname="05-admin-login.spec.js" time="0.14">
<system-out>
<![CDATA[管理员登录响应: {
  "success": true,
  "code": "200",
  "message": "登录成功",
  "data": {
    "accessToken": "mock-jwt-token-1753341131608",
    "tokenType": "Bearer",
    "refreshToken": null,
    "expiresIn": 3600,
    "userId": 1,
    "username": "admin",
    "role": "ADMIN"
  }
}
]]>
</system-out>
</testcase>
<testcase name="管理员账号登录测试 › 错误的管理员密码应该被拒绝" classname="05-admin-login.spec.js" time="0.135">
<failure message="05-admin-login.spec.js:69:3 错误的管理员密码应该被拒绝" type="FAILURE">
<![CDATA[  [chromium] › 05-admin-login.spec.js:69:3 › 管理员账号登录测试 › 错误的管理员密码应该被拒绝 ─────────────────────────────

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 401

      75 |     });
      76 |     
    > 77 |     expect(response.status()).toBe(200);
         |                               ^
      78 |     
      79 |     const data = await response.json();
      80 |     expect(data.success).toBe(false);
        at /Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js:77:31
]]>
</failure>
</testcase>
<testcase name="管理员账号登录测试 › 旧的管理员密码应该被拒绝" classname="05-admin-login.spec.js" time="0.135">
<failure message="05-admin-login.spec.js:84:3 旧的管理员密码应该被拒绝" type="FAILURE">
<![CDATA[  [chromium] › 05-admin-login.spec.js:84:3 › 管理员账号登录测试 › 旧的管理员密码应该被拒绝 ──────────────────────────────

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 401

      90 |     });
      91 |     
    > 92 |     expect(response.status()).toBe(200);
         |                               ^
      93 |     
      94 |     const data = await response.json();
      95 |     expect(data.success).toBe(false);
        at /Users/<USER>/ai/fs2/e2e-tests/tests/05-admin-login.spec.js:92:31
]]>
</failure>
</testcase>
<testcase name="管理员账号登录测试 › 管理员账号信息应该正确存储在数据库中" classname="05-admin-login.spec.js" time="0.118">
<system-out>
<![CDATA[管理员用户信息验证通过: { id: [33m1[39m, username: [32m'admin'[39m, role: [32m'ADMIN'[39m }
]]>
</system-out>
</testcase>
<testcase name="管理员账号登录测试 › 其他测试用户应该仍然可以正常登录" classname="05-admin-login.spec.js" time="0.237">
<system-out>
<![CDATA[其他测试用户验证通过
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>