<testsuites id="" name="" tests="3" failures="1" skipped="0" errors="0" time="16.614741">
<testsuite name="06-page-loading-fix.spec.js" timestamp="2025-07-24T07:23:12.583Z" hostname="chromium" tests="3" failures="1" skipped="0" time="30.484" errors="0">
<testcase name="页面加载修复验证 › 主页应该正常加载，不显示初始化失败错误" classname="06-page-loading-fix.spec.js" time="10.296">
<system-out>
<![CDATA[控制台消息:
[log] 应用配置已加载: {API_BASE_URL: http://localhost:8080/api, API_TIMEOUT: 30000, TOKEN_KEY: pm_access_token, REFRESH_TOKEN_KEY: pm_refresh_token, USER_KEY: pm_user_info}
[log] 当前环境: 开发环境
[log] 主JavaScript文件加载完成
[log] 应用初始化完成
✅ 页面加载测试完成
]]>
</system-out>
</testcase>
<testcase name="页面加载修复验证 › 登录页面应该正常加载" classname="06-page-loading-fix.spec.js" time="9.335">
<system-out>
<![CDATA[✅ 登录页面加载测试完成
]]>
</system-out>
</testcase>
<testcase name="页面加载修复验证 › JavaScript配置应该正确加载" classname="06-page-loading-fix.spec.js" time="10.853">
<failure message="06-page-loading-fix.spec.js:108:3 JavaScript配置应该正确加载" type="FAILURE">
<![CDATA[  [chromium] › 06-page-loading-fix.spec.js:108:3 › 页面加载修复验证 › JavaScript配置应该正确加载 ───────────────────

    Error: expect(received).toBeDefined()

    Received: undefined

      117 |     expect(appConfig).not.toBeNull();
      118 |     expect(appConfig.API_BASE_URL).toBeDefined();
    > 119 |     expect(appConfig.API_TIMEOUT).toBeDefined();
          |                                   ^
      120 |     
      121 |     console.log('APP_CONFIG:', appConfig);
      122 |     
        at /Users/<USER>/ai/fs2/e2e-tests/tests/06-page-loading-fix.spec.js:119:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/video.webm]]

[[ATTACHMENT|test-results/06-page-loading-fix-页面加载修复验证-JavaScript配置应该正确加载-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>