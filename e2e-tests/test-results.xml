<testsuites id="" name="" tests="7" failures="1" skipped="0" errors="0" time="17.910559999999997">
<testsuite name="01-basic-pages.spec.js" timestamp="2025-07-24T06:20:30.788Z" hostname="chromium" tests="7" failures="1" skipped="0" time="42.476" errors="0">
<testcase name="基础页面测试 › 主页应该正常加载（未登录时重定向到登录页）" classname="01-basic-pages.spec.js" time="5.35">
</testcase>
<testcase name="基础页面测试 › 登录页面应该正常加载" classname="01-basic-pages.spec.js" time="6.892">
</testcase>
<testcase name="基础页面测试 › 项目页面应该正常加载" classname="01-basic-pages.spec.js" time="4.328">
</testcase>
<testcase name="基础页面测试 › 任务页面应该正常加载" classname="01-basic-pages.spec.js" time="8.378">
<failure message="01-basic-pages.spec.js:60:3 任务页面应该正常加载" type="FAILURE">
<![CDATA[  [chromium] › 01-basic-pages.spec.js:60:3 › 基础页面测试 › 任务页面应该正常加载 ───────────────────────────────────

    Error: Timed out 5000ms waiting for expect(page).toHaveTitle(expected)

    Expected pattern: /任务|项目管理系统/
    Received string:  "Error response"
    Call log:
      - Expect "toHaveTitle" with timeout 5000ms
        9 × unexpected value "Error response"


      62 |     
      63 |     // 检查页面标题
    > 64 |     await expect(page).toHaveTitle(/任务|项目管理系统/);
         |                        ^
      65 |     
      66 |     // 检查页面主要内容
      67 |     await expect(page.locator('body')).toBeVisible();
        at /Users/<USER>/ai/fs2/e2e-tests/tests/01-basic-pages.spec.js:64:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/video.webm]]

[[ATTACHMENT|test-results/01-basic-pages-基础页面测试-任务页面应该正常加载-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="基础页面测试 › 用户页面应该正常加载" classname="01-basic-pages.spec.js" time="7.165">
</testcase>
<testcase name="基础页面测试 › 审核页面应该正常加载" classname="01-basic-pages.spec.js" time="5.196">
</testcase>
<testcase name="基础页面测试 › 报告页面应该正常加载" classname="01-basic-pages.spec.js" time="5.167">
</testcase>
</testsuite>
</testsuites>