#!/bin/bash

# 项目管理系统自动化测试运行脚本

echo "🚀 项目管理系统自动化测试"
echo "================================"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务状态
check_service() {
    local service_name="$1"
    local url="$2"
    
    echo -n "检查 $service_name ... "
    
    if curl -s "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 运行中${NC}"
        return 0
    else
        echo -e "${RED}✗ 未运行${NC}"
        return 1
    fi
}

# 检查前置条件
echo "1. 检查服务状态"
echo "----------------"

frontend_ok=false
backend_ok=false

if check_service "前端服务" "http://localhost:3000"; then
    frontend_ok=true
fi

if check_service "后端服务" "http://localhost:8080/actuator/health"; then
    backend_ok=true
fi

if [ "$frontend_ok" = false ] || [ "$backend_ok" = false ]; then
    echo
    echo -e "${YELLOW}⚠️  请确保以下服务正在运行:${NC}"
    if [ "$frontend_ok" = false ]; then
        echo "   前端服务: cd frontend && python3 -m http.server 3000"
    fi
    if [ "$backend_ok" = false ]; then
        echo "   后端服务: mvn spring-boot:run"
    fi
    echo
    read -p "服务已启动？按回车继续，或Ctrl+C退出..."
fi

echo
echo "2. 运行自动化测试"
echo "----------------"

# 创建截图目录
mkdir -p screenshots

# 运行测试的函数
run_test_suite() {
    local test_name="$1"
    local test_file="$2"
    local browser="${3:-chromium}"
    
    echo -e "${BLUE}运行 $test_name${NC}"
    
    if npx playwright test "$test_file" --project="$browser" --reporter=line; then
        echo -e "${GREEN}✓ $test_name 完成${NC}"
        return 0
    else
        echo -e "${RED}✗ $test_name 失败${NC}"
        return 1
    fi
}

# 测试计数器
total_suites=0
passed_suites=0

# 运行各个测试套件
echo

# API测试
((total_suites++))
if run_test_suite "API接口测试" "tests/02-api-tests.spec.js"; then
    ((passed_suites++))
fi

echo

# 基础页面测试
((total_suites++))
if run_test_suite "基础页面测试" "tests/01-basic-pages.spec.js"; then
    ((passed_suites++))
fi

echo

# 登录功能测试
((total_suites++))
if run_test_suite "登录功能测试" "tests/04-login-functionality.spec.js"; then
    ((passed_suites++))
fi

echo

# 用户交互测试
((total_suites++))
if run_test_suite "用户交互测试" "tests/03-user-interactions.spec.js"; then
    ((passed_suites++))
fi

echo
echo "3. 测试结果汇总"
echo "----------------"

echo "测试套件总数: $total_suites"
echo "通过套件数: $passed_suites"
echo "失败套件数: $((total_suites - passed_suites))"

if [ $passed_suites -eq $total_suites ]; then
    echo -e "${GREEN}🎉 所有测试套件通过！${NC}"
    exit_code=0
else
    echo -e "${YELLOW}⚠️  部分测试套件失败${NC}"
    exit_code=1
fi

echo
echo "4. 生成详细报告"
echo "----------------"

echo "生成HTML测试报告..."
npx playwright test --reporter=html > /dev/null 2>&1

echo -e "${BLUE}📊 查看详细报告:${NC}"
echo "   HTML报告: npx playwright show-report"
echo "   测试总结: cat TEST-SUMMARY.md"

echo
echo "5. 截图和视频"
echo "----------------"

screenshot_count=$(find screenshots -name "*.png" 2>/dev/null | wc -l)
video_count=$(find test-results -name "*.webm" 2>/dev/null | wc -l)

echo "测试截图: $screenshot_count 张"
echo "测试视频: $video_count 个"

if [ $screenshot_count -gt 0 ]; then
    echo "截图位置: screenshots/"
fi

if [ $video_count -gt 0 ]; then
    echo "视频位置: test-results/"
fi

echo
echo "================================"
if [ $exit_code -eq 0 ]; then
    echo -e "${GREEN}✅ 测试完成 - 系统状态良好${NC}"
else
    echo -e "${YELLOW}⚠️  测试完成 - 发现问题需要修复${NC}"
fi
echo "================================"

exit $exit_code
