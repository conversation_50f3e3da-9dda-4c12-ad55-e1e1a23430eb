# 项目管理系统 E2E 自动化测试

这是一个基于 Playwright 的端到端自动化测试套件，用于测试项目管理系统的前端和后端功能。

## 功能特性

- ✅ 多浏览器支持（Chromium、Firefox、WebKit）
- ✅ 移动端测试支持
- ✅ API 接口测试
- ✅ 用户界面交互测试
- ✅ 响应式设计测试
- ✅ 性能测试
- ✅ 截图和视频录制
- ✅ 详细的测试报告

## 安装和设置

### 前提条件

- Node.js 16+ 
- 项目管理系统后端服务运行在 http://localhost:8080
- 项目管理系统前端服务运行在 http://localhost:3000

### 安装依赖

```bash
cd e2e-tests
npm install
npx playwright install
```

## 运行测试

### 基本测试命令

```bash
# 运行所有测试
npm test

# 运行测试并显示浏览器界面
npm run test:headed

# 调试模式运行测试
npm run test:debug

# 使用测试UI界面
npm run test:ui

# 查看测试报告
npm run test:report
```

### 特定浏览器测试

```bash
# 只在 Chromium 中运行
npm run test:chromium

# 只在 Firefox 中运行
npm run test:firefox

# 只在 WebKit 中运行
npm run test:webkit

# 移动端测试
npm run test:mobile
```

### 运行特定测试文件

```bash
# 运行基础页面测试
npx playwright test tests/01-basic-pages.spec.js

# 运行API测试
npx playwright test tests/02-api-tests.spec.js

# 运行用户交互测试
npx playwright test tests/03-user-interactions.spec.js

# 运行登录功能测试
npx playwright test tests/04-login-functionality.spec.js
```

## 测试结构

```
e2e-tests/
├── tests/                          # 测试文件
│   ├── 01-basic-pages.spec.js      # 基础页面测试
│   ├── 02-api-tests.spec.js        # API接口测试
│   ├── 03-user-interactions.spec.js # 用户交互测试
│   └── 04-login-functionality.spec.js # 登录功能测试
├── utils/                          # 测试工具
│   └── test-helpers.js             # 测试辅助函数
├── test-data/                      # 测试数据
│   └── users.js                    # 用户测试数据
├── screenshots/                    # 测试截图
├── test-results/                   # 测试结果
├── playwright.config.js            # Playwright配置
├── global-setup.js                 # 全局设置
├── global-teardown.js              # 全局清理
└── README.md                       # 说明文档
```

## 测试覆盖范围

### 1. 基础页面测试 (01-basic-pages.spec.js)
- 主页加载测试
- 登录页面测试
- 项目页面测试
- 任务页面测试
- 用户页面测试
- 审核页面测试
- 报告页面测试

### 2. API接口测试 (02-api-tests.spec.js)
- 健康检查接口
- 测试接口
- 用户注册接口
- 用户登录接口
- 错误处理测试

### 3. 用户交互测试 (03-user-interactions.spec.js)
- 导航菜单功能
- 响应式设计
- 表单验证
- 页面性能
- JavaScript错误检测
- 链接功能测试

### 4. 登录功能测试 (04-login-functionality.spec.js)
- 成功登录流程
- 错误凭据处理
- 表单验证
- 键盘操作支持
- 密码隐藏功能
- 记住我功能

## 测试报告

测试完成后，会生成以下报告：

- **HTML报告**: `playwright-report/index.html`
- **JSON报告**: `test-results.json`
- **JUnit报告**: `test-results.xml`

查看HTML报告：
```bash
npm run test:report
```

## 截图和视频

- 测试截图保存在 `screenshots/` 目录
- 失败测试的视频保存在 `test-results/` 目录
- 测试轨迹文件保存在 `test-results/` 目录

## 配置说明

主要配置在 `playwright.config.js` 中：

- **baseURL**: 前端应用地址
- **timeout**: 测试超时时间
- **retries**: 失败重试次数
- **workers**: 并行执行数量
- **browsers**: 测试浏览器配置

## 故障排除

### 常见问题

1. **服务未启动**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3000
   ```
   解决：确保前端和后端服务都在运行

2. **浏览器未安装**
   ```
   Error: Executable doesn't exist
   ```
   解决：运行 `npx playwright install`

3. **测试超时**
   ```
   Error: Test timeout of 30000ms exceeded
   ```
   解决：检查网络连接或增加超时时间

### 调试技巧

1. **使用调试模式**
   ```bash
   npm run test:debug
   ```

2. **查看浏览器界面**
   ```bash
   npm run test:headed
   ```

3. **使用测试UI**
   ```bash
   npm run test:ui
   ```

4. **查看详细日志**
   ```bash
   npx playwright test --reporter=line
   ```

## 扩展测试

要添加新的测试：

1. 在 `tests/` 目录创建新的 `.spec.js` 文件
2. 使用 `test.describe()` 和 `test()` 组织测试
3. 使用 `utils/test-helpers.js` 中的辅助函数
4. 添加适当的断言和截图

示例：
```javascript
const { test, expect } = require('@playwright/test');
const { createTestUser, takeScreenshot } = require('../utils/test-helpers');

test.describe('新功能测试', () => {
  test('应该正常工作', async ({ page }) => {
    await page.goto('/new-feature');
    await expect(page.locator('.feature')).toBeVisible();
    await takeScreenshot(page, 'new-feature');
  });
});
```
