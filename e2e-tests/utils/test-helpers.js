// 测试辅助工具

/**
 * 创建测试用户
 */
async function createTestUser(request, userSuffix = Date.now()) {
  const testUser = {
    username: `testuser${userSuffix}`,
    email: `test${userSuffix}@example.com`,
    password: 'password123',
    fullName: `测试用户${userSuffix}`
  };

  const response = await request.post('http://localhost:8080/api/auth/register', {
    data: {
      username: testUser.username,
      email: testUser.email,
      password: testUser.password,
      confirmPassword: testUser.password,
      fullName: testUser.fullName
    }
  });

  return { testUser, response };
}

/**
 * 用户登录
 */
async function loginUser(request, username, password) {
  const response = await request.post('http://localhost:8080/api/auth/login', {
    data: {
      username,
      password
    }
  });

  const data = await response.json();
  return { response, token: data.data?.accessToken };
}

/**
 * 等待元素可见
 */
async function waitForElement(page, selector, timeout = 5000) {
  try {
    await page.waitForSelector(selector, { timeout, state: 'visible' });
    return true;
  } catch (error) {
    console.log(`元素 ${selector} 在 ${timeout}ms 内未出现`);
    return false;
  }
}

/**
 * 安全点击元素
 */
async function safeClick(page, selector) {
  try {
    await page.waitForSelector(selector, { state: 'visible' });
    await page.click(selector);
    return true;
  } catch (error) {
    console.log(`无法点击元素 ${selector}:`, error.message);
    return false;
  }
}

/**
 * 安全填写表单
 */
async function safeFill(page, selector, value) {
  try {
    await page.waitForSelector(selector, { state: 'visible' });
    await page.fill(selector, value);
    return true;
  } catch (error) {
    console.log(`无法填写元素 ${selector}:`, error.message);
    return false;
  }
}

/**
 * 检查API响应
 */
function validateApiResponse(response, expectedStatus = 200) {
  if (response.status() !== expectedStatus) {
    throw new Error(`API响应状态码错误: 期望 ${expectedStatus}, 实际 ${response.status()}`);
  }
}

/**
 * 生成随机字符串
 */
function generateRandomString(length = 8) {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 截图并保存
 */
async function takeScreenshot(page, name, path = 'screenshots') {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${path}/${name}-${timestamp}.png`;
  await page.screenshot({ path: filename, fullPage: true });
  console.log(`截图已保存: ${filename}`);
}

/**
 * 检查控制台错误
 */
function setupConsoleErrorTracking(page) {
  const errors = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  
  page.on('pageerror', error => {
    errors.push(error.message);
  });
  
  return errors;
}

module.exports = {
  createTestUser,
  loginUser,
  waitForElement,
  safeClick,
  safeFill,
  validateApiResponse,
  generateRandomString,
  takeScreenshot,
  setupConsoleErrorTracking
};
