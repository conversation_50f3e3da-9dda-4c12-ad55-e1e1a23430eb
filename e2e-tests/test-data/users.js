// 测试用户数据

const testUsers = {
  admin: {
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>',
    fullName: '系统管理员',
    role: 'ADMIN'
  },
  
  projectManager: {
    username: 'pm001',
    password: 'password123',
    email: '<EMAIL>',
    fullName: '项目经理',
    role: 'PROJECT_MANAGER'
  },
  
  developer: {
    username: 'dev001',
    password: 'password123',
    email: '<EMAIL>',
    fullName: '开发人员',
    role: 'MEMBER'
  },
  
  tester: {
    username: 'test001',
    password: 'password123',
    email: '<EMAIL>',
    fullName: '测试人员',
    role: 'MEMBER'
  }
};

const invalidUsers = {
  emptyUsername: {
    username: '',
    password: 'password123',
    email: '<EMAIL>',
    fullName: '空用户名'
  },
  
  emptyPassword: {
    username: 'nopassword',
    password: '',
    email: '<EMAIL>',
    fullName: '空密码'
  },
  
  invalidEmail: {
    username: 'invalidemail',
    password: 'password123',
    email: 'invalid-email',
    fullName: '无效邮箱'
  },
  
  shortPassword: {
    username: 'shortpass',
    password: '123',
    email: '<EMAIL>',
    fullName: '短密码'
  }
};

module.exports = {
  testUsers,
  invalidUsers
};
