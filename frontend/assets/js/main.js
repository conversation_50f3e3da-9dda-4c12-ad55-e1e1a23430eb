/**
 * 项目管理系统 - 主JavaScript文件
 * 包含应用初始化、路由管理和通用功能
 */

// 应用配置 - 扩展config.js中的配置
if (typeof APP_CONFIG !== 'undefined') {
    // 如果config.js已经定义了APP_CONFIG，则扩展它
    Object.assign(APP_CONFIG, {
        // H5特性配置
        MOBILE_BREAKPOINT: 768,
        TOUCH_THRESHOLD: 10,
        SWIPE_THRESHOLD: 100,
        DOUBLE_TAP_DELAY: 300
    });
} else {
    // 如果没有config.js，则定义基本配置
    const APP_CONFIG = {
        API_BASE_URL: 'http://localhost:8080/api',
        API_TIMEOUT: 30000,
        TOKEN_KEY: 'pms_token',
        USER_KEY: 'pms_user',
        REFRESH_TOKEN_KEY: 'pms_refresh_token',
        THEME_KEY: 'pms_theme',
        LANGUAGE_KEY: 'pms_language',
        // H5特性配置
        MOBILE_BREAKPOINT: 768,
        TOUCH_THRESHOLD: 10,
        SWIPE_THRESHOLD: 100,
        DOUBLE_TAP_DELAY: 300
    };
}

// 应用状态管理
class AppState {
    constructor() {
        this.user = null;
        this.token = null;
        this.currentPage = 'dashboard';
        this.theme = 'light';
        this.language = 'zh-CN';
        this.isMobile = false;
        this.isOnline = navigator.onLine;
        this.notifications = [];
        this.loading = false;
        this.authAPI = null;
        this.init();
    }

    /**
     * 初始化应用状态
     */
    init() {
        try {
            this.initAPI();
            this.detectMobile();
            this.loadUserFromStorage();
            this.loadThemeFromStorage();
            this.loadLanguageFromStorage();
            this.setupEventListeners();
            this.setupTouchEvents();
            this.setupNetworkEvents();
            this.checkAuthStatus();
            this.initServiceWorker();
        } catch (error) {
            console.error('AppState初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化API实例
     */
    initAPI() {
        if (typeof AuthAPI !== 'undefined') {
            this.authAPI = new AuthAPI();
        } else {
            console.warn('AuthAPI未定义，某些功能可能不可用');
        }
    }

    /**
     * 从本地存储加载用户信息
     */
    loadUserFromStorage() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const user = localStorage.getItem(APP_CONFIG.USER_KEY);
        
        if (token && user) {
            this.token = token;
            this.user = JSON.parse(user);
        }
    }

    /**
     * 保存用户信息到本地存储
     * @param {Object} user - 用户信息
     * @param {string} token - 访问令牌
     */
    saveUserToStorage(user, token) {
        this.user = user;
        this.token = token;
        localStorage.setItem(APP_CONFIG.TOKEN_KEY, token);
        localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(user));
    }

    /**
     * 清除用户信息
     */
    clearUserData() {
        this.user = null;
        this.token = null;
        localStorage.removeItem(APP_CONFIG.TOKEN_KEY);
        localStorage.removeItem(APP_CONFIG.USER_KEY);
        localStorage.removeItem(APP_CONFIG.REFRESH_TOKEN_KEY);
    }

    /**
     * 检查认证状态
     */
    checkAuthStatus() {
        if (!this.token) {
            this.redirectToLogin();
            return false;
        }
        return true;
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        window.location.href = 'pages/login.html';
    }

    /**
     * 检测移动设备
     */
    detectMobile() {
        const userAgent = navigator.userAgent;
        const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        const isMobileWidth = window.innerWidth <= APP_CONFIG.MOBILE_BREAKPOINT;
        
        this.isMobile = isMobileUA || isMobileWidth;
        
        // 更新body类名
        if (this.isMobile) {
            document.body.classList.add('mobile-device');
        } else {
            document.body.classList.remove('mobile-device');
        }
        
        return this.isMobile;
    }

    /**
     * 设置触摸事件
     */
    setupTouchEvents() {
        if (!('ontouchstart' in window)) return;
        
        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;
        let lastTap = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;
            
            // 处理滑动手势
            this.handleSwipe(touchStartX, touchStartY, touchEndX, touchEndY);
            
            // 处理双击
            const currentTime = new Date().getTime();
            const tapLength = currentTime - lastTap;
            if (tapLength < APP_CONFIG.DOUBLE_TAP_DELAY && tapLength > 0) {
                this.handleDoubleTap(e);
            }
            lastTap = currentTime;
        }, { passive: true });
    }

    /**
     * 处理滑动手势
     */
    handleSwipe(startX, startY, endX, endY) {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            if (Math.abs(deltaX) > APP_CONFIG.SWIPE_THRESHOLD) {
                if (deltaX > 0) {
                    this.onSwipeRight();
                } else {
                    this.onSwipeLeft();
                }
            }
        } else {
            // 垂直滑动
            if (Math.abs(deltaY) > APP_CONFIG.SWIPE_THRESHOLD) {
                if (deltaY > 0) {
                    this.onSwipeDown();
                } else {
                    this.onSwipeUp();
                }
            }
        }
    }

    /**
     * 处理双击事件
     */
    handleDoubleTap(e) {
        // 可以在这里添加双击处理逻辑
        console.log('Double tap detected');
    }

    /**
     * 右滑事件
     */
    onSwipeRight() {
        // 在移动端可以用于打开侧边栏
        if (this.isMobile) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && sidebar.classList.contains('collapsed')) {
                this.toggleSidebar();
            }
        }
    }

    /**
     * 左滑事件
     */
    onSwipeLeft() {
        // 在移动端可以用于关闭侧边栏
        if (this.isMobile) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && !sidebar.classList.contains('collapsed')) {
                this.toggleSidebar();
            }
        }
    }

    /**
     * 上滑事件
     */
    onSwipeUp() {
        // 可以用于刷新页面或其他操作
    }

    /**
     * 下滑事件
     */
    onSwipeDown() {
        // 可以用于下拉刷新
    }

    /**
     * 设置网络事件监听
     */
    setupNetworkEvents() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showNotification('网络连接已恢复', 'success');
            // 重新同步数据
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showNotification('网络连接已断开，将在离线模式下工作', 'warning');
        });
    }

    /**
     * 初始化Service Worker
     */
    async initServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker注册成功:', registration);
            } catch (error) {
                console.log('Service Worker注册失败:', error);
            }
        }
    }

    /**
     * 从本地存储加载主题
     */
    loadThemeFromStorage() {
        const savedTheme = localStorage.getItem(APP_CONFIG.THEME_KEY);
        if (savedTheme) {
            this.theme = savedTheme;
        } else {
            // 检测系统主题偏好
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                this.theme = 'dark';
            }
        }
        this.applyTheme(this.theme);
    }

    /**
     * 应用主题
     */
    applyTheme(theme) {
        this.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        
        if (theme === 'dark') {
            document.body.classList.add('dark-theme');
        } else {
            document.body.classList.remove('dark-theme');
        }
        
        localStorage.setItem(APP_CONFIG.THEME_KEY, theme);
        
        // 更新主题切换按钮状态
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const newTheme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    /**
     * 从本地存储加载语言
     */
    loadLanguageFromStorage() {
        const savedLanguage = localStorage.getItem(APP_CONFIG.LANGUAGE_KEY);
        if (savedLanguage) {
            this.language = savedLanguage;
        }
    }

    /**
     * 设置语言
     */
    setLanguage(language) {
        this.language = language;
        localStorage.setItem(APP_CONFIG.LANGUAGE_KEY, language);
        document.documentElement.lang = language;
    }

    /**
     * 切换侧边栏
     */
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
            
            if (this.isMobile) {
                if (overlay) {
                    overlay.classList.toggle('show');
                }
                document.body.classList.toggle('sidebar-open');
            }
        }
    }

    /**
     * 处理屏幕方向变化
     */
    handleOrientationChange() {
        // 重新计算布局
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 100);
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 在移动端自动收起侧边栏
        if (this.isMobile) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && !sidebar.classList.contains('collapsed')) {
                this.toggleSidebar();
            }
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };
        
        this.notifications.push(notification);
        Utils.showToast(message, type);
        
        // 自动移除通知
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification.id);
            }, duration);
        }
    }

    /**
     * 移除通知
     */
    removeNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
    }

    /**
     * 同步离线数据
     */
    async syncOfflineData() {
        // 这里可以添加离线数据同步逻辑
        console.log('开始同步离线数据...');
    }

    /**
     * 设置加载状态
     */
    setLoading(loading) {
        this.loading = loading;
        const loadingEl = document.getElementById('global-loading');
        if (loadingEl) {
            if (loading) {
                loadingEl.classList.add('show');
            } else {
                loadingEl.classList.remove('show');
            }
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 导航链接点击事件
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                e.preventDefault();
                const page = link.getAttribute('href').substring(1);
                this.navigateTo(page);
            }
        });

        // 页面刷新时保持当前页面
        window.addEventListener('beforeunload', () => {
            sessionStorage.setItem('currentPage', this.currentPage);
        });

        // 页面加载时恢复页面状态
        window.addEventListener('load', () => {
            const savedPage = sessionStorage.getItem('currentPage');
            if (savedPage) {
                this.navigateTo(savedPage);
            }
        });

        // 监听屏幕方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.detectMobile();
                this.handleOrientationChange();
            }, 100);
        });

        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            this.detectMobile();
            this.handleResize();
        }, 250));

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.token) {
                this.checkAuthStatus();
            }
        });

        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === APP_CONFIG.TOKEN_KEY) {
                if (!e.newValue) {
                    this.clearUserData();
                    this.redirectToLogin();
                } else if (e.newValue !== this.token) {
                    this.loadUserFromStorage();
                }
            }
        });

        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem(APP_CONFIG.THEME_KEY)) {
                    this.theme = e.matches ? 'dark' : 'light';
                    this.applyTheme(this.theme);
                }
            });
        }
    }

    /**
     * 页面导航
     * @param {string} page - 页面名称
     */
    navigateTo(page) {
        this.currentPage = page;
        this.updateActiveNavigation(page);
        this.loadPageContent(page);
    }

    /**
     * 更新导航状态
     * @param {string} activePage - 当前激活页面
     */
    updateActiveNavigation(activePage) {
        // 移除所有活动状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // 添加当前页面的活动状态
        document.querySelectorAll(`a[href="#${activePage}"]`).forEach(link => {
            link.classList.add('active');
        });
    }

    /**
     * 加载页面内容
     * @param {string} page - 页面名称
     */
    async loadPageContent(page) {
        const contentContainer = document.getElementById('app-content');
        
        try {
            // 显示加载状态
            this.showLoading(contentContainer);

            // 根据页面名称加载对应内容
            switch (page) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'projects':
                    await this.loadProjects();
                    break;
                case 'tasks':
                    await this.loadTasks();
                    break;
                case 'reports':
                    await this.loadReports();
                    break;
                case 'approvals':
                    await this.loadApprovals();
                    break;
                default:
                    this.showNotFound();
            }
        } catch (error) {
            console.error('加载页面内容失败:', error);
            this.showError('页面加载失败，请稍后重试');
        }
    }

    /**
     * 显示加载状态
     * @param {HTMLElement} container - 容器元素
     */
    showLoading(container) {
        container.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                <div class="text-center">
                    <div class="loading mb-3"></div>
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }

    /**
     * 显示404页面
     */
    showNotFound() {
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h3>页面未找到</h3>
                <p class="text-muted">您访问的页面不存在</p>
                <button class="btn btn-primary" onclick="appState.navigateTo('dashboard')">
                    返回首页
                </button>
            </div>
        `;
    }

    /**
     * 加载仪表板
     */
    async loadDashboard() {
        if (typeof Dashboard !== 'undefined') {
            await Dashboard.render();
        } else {
            console.error('Dashboard组件未加载');
        }
    }

    /**
     * 加载项目页面
     */
    async loadProjects() {
        // 项目页面内容将在projects.js中实现
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">项目管理</h1>
                <button class="btn btn-primary" onclick="createProject()">
                    <i class="fas fa-plus me-2"></i>新建项目
                </button>
            </div>
            <div id="projects-content">
                <!-- 项目列表将在这里加载 -->
            </div>
        `;
    }

    /**
     * 加载任务页面
     */
    async loadTasks() {
        // 任务页面内容将在tasks.js中实现
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">任务管理</h1>
                <button class="btn btn-primary" onclick="createTask()">
                    <i class="fas fa-plus me-2"></i>新建任务
                </button>
            </div>
            <div id="tasks-content">
                <!-- 任务列表将在这里加载 -->
            </div>
        `;
    }

    /**
     * 加载报告页面
     */
    async loadReports() {
        try {
            const response = await fetch('pages/reports.html');
            const html = await response.text();
            
            // 提取body内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const bodyContent = doc.body.innerHTML;
            
            const contentContainer = document.getElementById('app-content');
            contentContainer.innerHTML = bodyContent;
            
            // 加载报告页面的CSS和JS
            this.loadReportAssets();
        } catch (error) {
            console.error('加载报告页面失败:', error);
            this.showError('报告页面加载失败，请稍后重试');
        }
    }

    /**
     * 加载报告页面资源
     */
    loadReportAssets() {
        // 加载CSS
        if (!document.querySelector('link[href*="reports.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'assets/css/reports.css';
            document.head.appendChild(link);
        }
        
        // 加载JS
        if (!document.querySelector('script[src*="reports.js"]')) {
            const script = document.createElement('script');
            script.src = 'assets/js/reports.js';
            document.head.appendChild(script);
        }
    }

    /**
     * 加载审核页面
     */
    async loadApprovals() {
        try {
            const contentContainer = document.getElementById('app-content');
            const response = await fetch('../pages/approvals.html');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const html = await response.text();
            contentContainer.innerHTML = html;
            
            // 动态加载CSS和JS
            await this.loadApprovalAssets();
            
        } catch (error) {
            console.error('加载审核页面失败:', error);
            this.showError('加载审核页面失败，请稍后重试');
        }
    }
    
    async loadApprovalAssets() {
        // 加载CSS
        if (!document.querySelector('link[href="../assets/css/approvals.css"]')) {
            const cssLink = document.createElement('link');
            cssLink.rel = 'stylesheet';
            cssLink.href = '../assets/css/approvals.css';
            document.head.appendChild(cssLink);
        }
        
        // 加载JS
        if (!document.querySelector('script[src="../assets/js/approvals.js"]')) {
            const script = document.createElement('script');
            script.src = '../assets/js/approvals.js';
            document.body.appendChild(script);
            
            // 等待脚本加载完成
            return new Promise((resolve) => {
                script.onload = resolve;
            });
        }
        
        return Promise.resolve();
    }
}

// 工具函数
const Utils = {
    /**
     * 格式化日期
     * @param {string|Date} date - 日期
     * @param {string} format - 格式
     * @returns {string} 格式化后的日期
     */
    formatDate(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 获取相对时间
     * @param {string|Date} date - 日期
     * @returns {string} 相对时间描述
     */
    getRelativeTime(date) {
        const now = new Date();
        const diff = now - new Date(date);
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) {
            return `${days}天前`;
        } else if (hours > 0) {
            return `${hours}小时前`;
        } else if (minutes > 0) {
            return `${minutes}分钟前`;
        } else {
            return '刚刚';
        }
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化数字
     * @param {number} num - 数字
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的数字
     */
    formatNumber(num, decimals = 0) {
        return new Intl.NumberFormat('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(num);
    },

    /**
     * 生成UUID
     * @returns {string} UUID字符串
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => Utils.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * HTML转义
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * 获取URL参数
     * @returns {Object} URL参数对象
     */
    getUrlParams() {
        const params = {};
        const urlSearchParams = new URLSearchParams(window.location.search);
        for (const [key, value] of urlSearchParams) {
            params[key] = value;
        }
        return params;
    },

    /**
     * 设置URL参数
     * @param {Object} params - 参数对象
     */
    setUrlParams(params) {
        const url = new URL(window.location);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.history.replaceState({}, '', url);
    },

    /**
     * 下载文件
     * @param {Blob} blob - 文件Blob对象
     * @param {string} filename - 文件名
     */
    downloadFile(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    },

    /**
     * 复制到剪贴板
     * @param {string} text - 要复制的文本
     * @returns {Promise<boolean>} 是否成功复制
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
                return true;
            } catch (err) {
                return false;
            } finally {
                document.body.removeChild(textArea);
            }
        }
    },

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    /**
     * 验证手机号格式
     * @param {string} phone - 手机号
     * @returns {boolean} 是否有效
     */
    validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    },

    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {Object} 验证结果
     */
    validatePassword(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        return {
            isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers,
            strength: {
                length: password.length >= minLength,
                upperCase: hasUpperCase,
                lowerCase: hasLowerCase,
                numbers: hasNumbers,
                specialChar: hasSpecialChar
            }
        };
    },

    /**
     * 获取文件扩展名
     * @param {string} filename - 文件名
     * @returns {string} 扩展名
     */
    getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    },

    /**
     * 检查是否为图片文件
     * @param {string} filename - 文件名
     * @returns {boolean} 是否为图片
     */
    isImageFile(filename) {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const extension = Utils.getFileExtension(filename).toLowerCase();
        return imageExtensions.includes(extension);
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间间隔
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    showToast(message, type = 'info') {
        const toastContainer = this.getOrCreateToastContainer();
        const toastId = 'toast-' + Date.now();
        
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        
        toast.show();
        
        // 自动移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    },

    /**
     * 获取或创建Toast容器
     * @returns {HTMLElement} Toast容器
     */
    getOrCreateToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }
        return container;
    },

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 检查设备类型
     * @returns {Object} 设备信息
     */
    getDeviceInfo() {
        const userAgent = navigator.userAgent;
        return {
            isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
            isTablet: /iPad|Android(?!.*Mobile)/i.test(userAgent),
            isDesktop: !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
            isIOS: /iPad|iPhone|iPod/.test(userAgent),
            isAndroid: /Android/.test(userAgent),
            isSafari: /Safari/.test(userAgent) && !/Chrome/.test(userAgent),
            isChrome: /Chrome/.test(userAgent),
            isFirefox: /Firefox/.test(userAgent)
        };
    },

    /**
     * 获取浏览器信息
     * @returns {Object} 浏览器信息
     */
    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        let browserName = 'Unknown';
        let browserVersion = 'Unknown';
        
        if (userAgent.indexOf('Chrome') > -1) {
            browserName = 'Chrome';
            browserVersion = userAgent.match(/Chrome\/(\d+)/)[1];
        } else if (userAgent.indexOf('Firefox') > -1) {
            browserName = 'Firefox';
            browserVersion = userAgent.match(/Firefox\/(\d+)/)[1];
        } else if (userAgent.indexOf('Safari') > -1) {
            browserName = 'Safari';
            browserVersion = userAgent.match(/Version\/(\d+)/)[1];
        } else if (userAgent.indexOf('Edge') > -1) {
            browserName = 'Edge';
            browserVersion = userAgent.match(/Edge\/(\d+)/)[1];
        }
        
        return {
            name: browserName,
            version: browserVersion,
            userAgent: userAgent
        };
    },

    /**
     * 本地存储操作
     */
    storage: {
        /**
         * 设置本地存储
         * @param {string} key - 键
         * @param {any} value - 值
         */
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (error) {
                console.error('设置本地存储失败:', error);
            }
        },
        
        /**
         * 获取本地存储
         * @param {string} key - 键
         * @param {any} defaultValue - 默认值
         * @returns {any} 存储的值
         */
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('获取本地存储失败:', error);
                return defaultValue;
            }
        },
        
        /**
         * 移除本地存储
         * @param {string} key - 键
         */
        remove(key) {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.error('移除本地存储失败:', error);
            }
        },
        
        /**
         * 清空本地存储
         */
        clear() {
            try {
                localStorage.clear();
            } catch (error) {
                console.error('清空本地存储失败:', error);
            }
        }
    },

    /**
     * 会话存储操作
     */
    sessionStorage: {
        /**
         * 设置会话存储
         * @param {string} key - 键
         * @param {any} value - 值
         */
        set(key, value) {
            try {
                sessionStorage.setItem(key, JSON.stringify(value));
            } catch (error) {
                console.error('设置会话存储失败:', error);
            }
        },
        
        /**
         * 获取会话存储
         * @param {string} key - 键
         * @param {any} defaultValue - 默认值
         * @returns {any} 存储的值
         */
        get(key, defaultValue = null) {
            try {
                const item = sessionStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('获取会话存储失败:', error);
                return defaultValue;
            }
        },
        
        /**
         * 移除会话存储
         * @param {string} key - 键
         */
        remove(key) {
            try {
                sessionStorage.removeItem(key);
            } catch (error) {
                console.error('移除会话存储失败:', error);
            }
        },
        
        /**
         * 清空会话存储
         */
        clear() {
            try {
                sessionStorage.clear();
            } catch (error) {
                console.error('清空会话存储失败:', error);
            }
        }
    },

    /**
     * 加载脚本
     * @param {string} src - 脚本地址
     * @returns {Promise} 加载Promise
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    },

    /**
     * 加载样式
     * @param {string} href - 样式地址
     * @returns {Promise} 加载Promise
     */
    loadStyle(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    },

    /**
     * 等待指定时间
     * @param {number} ms - 毫秒数
     * @returns {Promise} 等待Promise
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * 重试函数
     * @param {Function} fn - 要重试的函数
     * @param {number} retries - 重试次数
     * @param {number} delay - 重试间隔
     * @returns {Promise} 重试Promise
     */
    async retry(fn, retries = 3, delay = 1000) {
        try {
            return await fn();
        } catch (error) {
            if (retries > 0) {
                await this.sleep(delay);
                return this.retry(fn, retries - 1, delay);
            }
            throw error;
        }
    }
};

// 全局函数
/**
 * 全局登出函数
 */
function logout() {
    if (window.appState) {
        if (confirm('确定要退出登录吗？')) {
            window.appState.clearUserData();
            window.appState.redirectToLogin();
            Utils.showToast('已成功登出', 'success');
        }
    }
}

/**
 * 全局创建项目函数
 */
function createProject() {
    if (window.appState) {
        // 显示创建项目模态框
        const modal = document.getElementById('createProjectModal');
        if (modal) {
            modal.style.display = 'block';
        } else {
            // 如果模态框不存在，创建一个简单的提示
            const projectName = prompt('请输入项目名称:');
            if (projectName) {
                // TODO: 调用API创建项目
                Utils.showToast(`项目 "${projectName}" 创建成功`, 'success');
            }
        }
    }
}

/**
 * 全局创建任务函数
 */
function createTask() {
    if (window.appState) {
        // 显示创建任务模态框
        const modal = document.getElementById('createTaskModal');
        if (modal) {
            modal.style.display = 'block';
        } else {
            // 如果模态框不存在，创建一个简单的提示
            const taskName = prompt('请输入任务名称:');
            if (taskName) {
                // TODO: 调用API创建任务
                Utils.showToast(`任务 "${taskName}" 创建成功`, 'success');
            }
        }
    }
}

/**
 * 全局搜索函数
 * @param {string} query - 搜索关键词
 */
function globalSearch(query) {
    if (window.appState && query.trim()) {
        // TODO: 实现全局搜索逻辑
        console.log('搜索:', query);
        Utils.showToast(`搜索 "${query}" 的结果`, 'info');
    }
}

/**
 * 切换主题
 */
function toggleTheme() {
    if (window.appState) {
        window.appState.toggleTheme();
    }
}

/**
 * 切换侧边栏
 */
function toggleSidebar() {
    if (window.appState) {
        window.appState.toggleSidebar();
    }
}

/**
 * 切换语言
 * @param {string} lang - 语言代码
 */
function setLanguage(lang) {
    if (window.appState) {
        window.appState.setLanguage(lang);
    }
}

/**
 * 显示通知
 * @param {string} message - 通知消息
 * @param {string} type - 通知类型
 * @param {number} duration - 显示时长
 */
function showNotification(message, type = 'info', duration = 3000) {
    if (window.appState) {
        window.appState.showNotification(message, type, duration);
    } else {
        Utils.showToast(message, type);
    }
}

/**
 * 检查用户权限
 * @param {string} permission - 权限名称
 * @returns {boolean} 是否有权限
 */
function hasPermission(permission) {
    if (window.appState && window.appState.user) {
        return window.appState.user.permissions && window.appState.user.permissions.includes(permission);
    }
    return false;
}

/**
 * 检查用户角色
 * @param {string} role - 角色名称
 * @returns {boolean} 是否有角色
 */
function hasRole(role) {
    if (window.appState && window.appState.user) {
        return window.appState.user.role === role;
    }
    return false;
}

/**
 * 格式化日期
 * @param {Date|string} date - 日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期
 */
function formatDate(date, format = 'YYYY-MM-DD') {
    return Utils.formatDate(date, format);
}

/**
 * 获取相对时间
 * @param {Date|string} date - 日期
 * @returns {string} 相对时间
 */
function getRelativeTime(date) {
    return Utils.getRelativeTime(date);
}

/**
 * 复制到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    Utils.copyToClipboard(text).then(() => {
        showNotification('已复制到剪贴板', 'success');
    }).catch(() => {
        showNotification('复制失败', 'error');
    });
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 */
function downloadFile(url, filename) {
    Utils.downloadFile(url, filename);
}

// 应用初始化
let appState;

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        // 创建应用状态实例
        appState = new AppState();
        
        // 设置全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            if (appState) {
                appState.showNotification('发生了一个错误，请刷新页面重试', 'error');
            }
        });
        
        // 设置未处理的Promise拒绝处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
            if (appState) {
                appState.showNotification('网络请求失败，请检查网络连接', 'error');
            }
        });
        
        // 设置键盘快捷键
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + K: 全局搜索
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                const searchInput = document.querySelector('#globalSearch');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Ctrl/Cmd + Shift + T: 切换主题
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
                event.preventDefault();
                toggleTheme();
            }
            
            // Ctrl/Cmd + Shift + S: 切换侧边栏
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
                event.preventDefault();
                toggleSidebar();
            }
            
            // ESC: 关闭模态框
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal:not([style*="display: none"])');
                modals.forEach(modal => {
                    modal.style.display = 'none';
                });
            }
        });
        
        // 如果用户已登录，加载默认页面
        if (appState.checkAuthStatus()) {
            appState.navigateTo('dashboard');
        }
        
        // 初始化完成后的处理
        console.log('应用初始化完成');
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        // 显示错误信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            max-width: 400px;
            text-align: center;
        `;
        errorDiv.innerHTML = `
            <h3>应用初始化失败</h3>
            <p>请刷新页面重试，如果问题持续存在，请联系技术支持。</p>
            <button onclick="location.reload()" style="
                background: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        document.body.appendChild(errorDiv);
    }
});

// 导出全局对象
window.APP_CONFIG = APP_CONFIG;
window.Utils = Utils;
window.appState = appState;

// 导出全局函数
window.logout = logout;
window.createProject = createProject;
window.createTask = createTask;
window.globalSearch = globalSearch;
window.toggleTheme = toggleTheme;
window.toggleSidebar = toggleSidebar;
window.setLanguage = setLanguage;
window.showNotification = showNotification;
window.hasPermission = hasPermission;
window.hasRole = hasRole;
window.formatDate = formatDate;
window.getRelativeTime = getRelativeTime;
window.copyToClipboard = copyToClipboard;
window.downloadFile = downloadFile;

// 兼容性检查
if (typeof Promise === 'undefined') {
    console.error('浏览器不支持Promise，请升级浏览器');
}

if (typeof fetch === 'undefined') {
    console.error('浏览器不支持fetch API，请升级浏览器');
}

// 性能监控
if (window.performance && window.performance.mark) {
    window.performance.mark('app-init-start');
    
    window.addEventListener('load', () => {
        window.performance.mark('app-init-end');
        window.performance.measure('app-init', 'app-init-start', 'app-init-end');
        
        const measure = window.performance.getEntriesByName('app-init')[0];
        if (measure) {
            console.log(`应用初始化耗时: ${measure.duration.toFixed(2)}ms`);
        }
    });
}

console.log('主JavaScript文件加载完成');