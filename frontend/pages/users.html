<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 项目管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/users.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-project-diagram me-2"></i>
                项目管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-folder me-1"></i>项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tasks.html">
                            <i class="fas fa-tasks me-1"></i>任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-chart-bar me-1"></i>报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="approvals.html">
                            <i class="fas fa-check-circle me-1"></i>审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.html">
                            <i class="fas fa-users me-1"></i>用户
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">用户管理</h1>
                    <p class="text-muted mb-0">管理系统用户和权限</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <i class="fas fa-plus me-2"></i>新建用户
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="totalUsers">0</h5>
                                    <p class="card-text text-muted">总用户数</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="activeUsers">0</h5>
                                    <p class="card-text text-muted">活跃用户</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-user-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="pendingUsers">0</h5>
                                    <p class="card-text text-muted">待审核</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="newUsersToday">0</h5>
                                    <p class="card-text text-muted">今日新增</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">搜索用户</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="用户名、邮箱或姓名">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">状态</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">非活跃</option>
                                <option value="pending">待审核</option>
                                <option value="suspended">已暂停</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">角色</label>
                            <select class="form-select" id="roleFilter">
                                <option value="">全部角色</option>
                                <option value="admin">管理员</option>
                                <option value="manager">项目经理</option>
                                <option value="member">成员</option>
                                <option value="viewer">查看者</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">部门</label>
                            <select class="form-select" id="departmentFilter">
                                <option value="">全部部门</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">操作</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary" id="resetFilters">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="button" class="btn btn-outline-success" id="exportUsers">
                                    <i class="fas fa-download me-1"></i>导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">用户列表</h5>
                    <div class="d-flex align-items-center gap-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showInactiveUsers">
                            <label class="form-check-label" for="showInactiveUsers">
                                显示非活跃用户
                            </label>
                        </div>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="viewMode" id="listView" checked>
                            <label class="btn btn-outline-secondary btn-sm" for="listView">
                                <i class="fas fa-list"></i>
                            </label>
                            <input type="radio" class="btn-check" name="viewMode" id="cardView">
                            <label class="btn btn-outline-secondary btn-sm" for="cardView">
                                <i class="fas fa-th"></i>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 加载状态 -->
                    <div id="loadingState" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载用户数据...</p>
                    </div>

                    <!-- 列表视图 -->
                    <div id="listViewContainer" class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                        </div>
                                    </th>
                                    <th>用户</th>
                                    <th>角色</th>
                                    <th>部门</th>
                                    <th>状态</th>
                                    <th>最后登录</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- 用户数据将在这里动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 卡片视图 -->
                    <div id="cardViewContainer" class="row g-3 p-3" style="display: none;">
                        <!-- 用户卡片将在这里动态加载 -->
                    </div>

                    <!-- 空状态 -->
                    <div id="emptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无用户数据</h5>
                        <p class="text-muted">点击上方按钮创建第一个用户</p>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- 分页按钮将在这里动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建用户模态框 -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createUserForm">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="full_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">手机号</label>
                                <input type="tel" class="form-control" name="phone">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">角色 <span class="text-danger">*</span></label>
                                <select class="form-select" name="role" required>
                                    <option value="">请选择角色</option>
                                    <option value="admin">管理员</option>
                                    <option value="manager">项目经理</option>
                                    <option value="member">成员</option>
                                    <option value="viewer">查看者</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">部门</label>
                                <select class="form-select" name="department_id">
                                    <option value="">请选择部门</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">密码 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">确认密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label">备注</label>
                                <textarea class="form-control" name="notes" rows="3" placeholder="用户备注信息"></textarea>
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="send_welcome_email" id="sendWelcomeEmail" checked>
                                    <label class="form-check-label" for="sendWelcomeEmail">
                                        发送欢迎邮件
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>创建用户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div class="modal fade" id="userDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userDetailContent">
                    <!-- 用户详情内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="../assets/js/config.js"></script>
    <script src="../assets/js/api/auth.js"></script>
    <script src="../assets/js/api/users.js"></script>
    <script src="../assets/js/users.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>