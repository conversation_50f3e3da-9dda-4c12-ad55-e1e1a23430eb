<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端集成测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs me-2"></i>
                            项目管理系统 - 前后端集成测试
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- 认证测试区域 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5><i class="fas fa-user-shield me-2"></i>认证功能测试</h5>
                                <div class="btn-group mb-3" role="group">
                                    <button id="testRegisterBtn" class="btn btn-outline-primary">
                                        <i class="fas fa-user-plus me-1"></i>测试注册
                                    </button>
                                    <button id="testLoginBtn" class="btn btn-outline-success">
                                        <i class="fas fa-sign-in-alt me-1"></i>测试登录
                                    </button>
                                    <button id="testLogoutBtn" class="btn btn-outline-secondary">
                                        <i class="fas fa-sign-out-alt me-1"></i>测试登出
                                    </button>
                                    <button id="testCurrentUserBtn" class="btn btn-outline-info">
                                        <i class="fas fa-user me-1"></i>获取当前用户
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 项目功能测试区域 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5><i class="fas fa-project-diagram me-2"></i>项目功能测试</h5>
                                <div class="btn-group mb-3" role="group">
                                    <button id="testProjectsBtn" class="btn btn-outline-primary">
                                        <i class="fas fa-list me-1"></i>项目列表
                                    </button>
                                    <button id="testCreateProjectBtn" class="btn btn-outline-success">
                                        <i class="fas fa-plus me-1"></i>创建项目
                                    </button>
                                    <button id="testProjectDetailBtn" class="btn btn-outline-info">
                                        <i class="fas fa-eye me-1"></i>项目详情
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 任务功能测试区域 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5><i class="fas fa-tasks me-2"></i>任务功能测试</h5>
                                <div class="btn-group mb-3" role="group">
                                    <button id="testTasksBtn" class="btn btn-outline-primary">
                                        <i class="fas fa-list-check me-1"></i>任务列表
                                    </button>
                                    <button id="testCreateTaskBtn" class="btn btn-outline-success">
                                        <i class="fas fa-plus me-1"></i>创建任务
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 系统测试区域 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5><i class="fas fa-server me-2"></i>系统功能测试</h5>
                                <div class="btn-group mb-3" role="group">
                                    <button id="testHealthBtn" class="btn btn-outline-primary">
                                        <i class="fas fa-heartbeat me-1"></i>健康检查
                                    </button>
                                    <button id="testStatusBtn" class="btn btn-outline-info">
                                        <i class="fas fa-info-circle me-1"></i>系统状态
                                    </button>
                                    <button id="runAllTestsBtn" class="btn btn-warning">
                                        <i class="fas fa-play me-1"></i>运行所有测试
                                    </button>
                                    <button id="clearBtn" class="btn btn-outline-danger">
                                        <i class="fas fa-trash me-1"></i>清空结果
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 当前状态显示 -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <strong>当前状态:</strong>
                                    <span id="currentStatus">未登录</span>
                                    <span id="currentUser" class="ms-3"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 测试结果显示区域 -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5><i class="fas fa-clipboard-list me-2"></i>测试结果</h5>
                                <div id="results" class="border rounded p-3" style="height: 400px; overflow-y: auto; background-color: #f8f9fa;">
                                    <div class="text-muted text-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        点击上方按钮开始测试...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:8080';
        let currentToken = localStorage.getItem('test_token');
        let currentUserData = null;

        // 更新状态显示
        function updateStatus() {
            const statusElement = document.getElementById('currentStatus');
            const userElement = document.getElementById('currentUser');
            
            if (currentToken) {
                statusElement.textContent = '已登录';
                statusElement.className = 'text-success';
                if (currentUserData) {
                    userElement.innerHTML = `<i class="fas fa-user me-1"></i>${currentUserData.username} (${currentUserData.role})`;
                }
            } else {
                statusElement.textContent = '未登录';
                statusElement.className = 'text-danger';
                userElement.innerHTML = '';
            }
        }

        // 添加测试结果
        function addResult(message, type = 'info', data = null) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} mb-2`;
            
            let content = `<strong>${timestamp}</strong>: ${message}`;
            if (data) {
                content += `<pre class="mt-2 mb-0" style="font-size: 0.8em;">${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            alertDiv.innerHTML = content;
            results.appendChild(alertDiv);
            results.scrollTop = results.scrollHeight;
        }

        // 通用API请求函数
        async function apiRequest(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (currentToken) {
                defaultOptions.headers['Authorization'] = `Bearer ${currentToken}`;
            }

            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            return fetch(`${API_BASE_URL}${endpoint}`, finalOptions);
        }

        // 认证功能测试
        document.getElementById('testRegisterBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试用户注册...', 'info');
                const testUser = {
                    username: 'testuser' + Date.now(),
                    email: 'test' + Date.now() + '@example.com',
                    fullName: '测试用户',
                    password: 'password123',
                    confirmPassword: 'password123'
                };

                const response = await apiRequest('/api/auth/register', {
                    method: 'POST',
                    body: JSON.stringify(testUser)
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('注册测试成功', 'success', data);
                    window.testUser = testUser;
                } else {
                    const errorData = await response.json();
                    addResult('注册测试失败', 'error', errorData);
                }
            } catch (error) {
                addResult(`注册测试错误: ${error.message}`, 'error');
            }
        });

        document.getElementById('testLoginBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试用户登录...', 'info');

                const loginData = window.testUser ? {
                    username: window.testUser.username,
                    password: window.testUser.password
                } : {
                    username: 'testuser',
                    password: 'password123'
                };

                const response = await apiRequest('/api/auth/login', {
                    method: 'POST',
                    body: JSON.stringify(loginData)
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('登录测试成功', 'success', data);
                    if (data.data && data.data.accessToken) {
                        currentToken = data.data.accessToken;
                        localStorage.setItem('test_token', currentToken);
                        currentUserData = {
                            username: data.data.username,
                            role: data.data.role
                        };
                        updateStatus();
                    }
                } else {
                    const errorData = await response.json();
                    addResult('登录测试失败', 'error', errorData);
                }
            } catch (error) {
                addResult(`登录测试错误: ${error.message}`, 'error');
            }
        });

        // 其他测试功能
        document.getElementById('testLogoutBtn').addEventListener('click', () => {
            currentToken = null;
            currentUserData = null;
            localStorage.removeItem('test_token');
            updateStatus();
            addResult('已登出', 'info');
        });

        document.getElementById('testCurrentUserBtn').addEventListener('click', async () => {
            try {
                addResult('正在获取当前用户信息...', 'info');
                const response = await apiRequest('/api/auth/me');

                if (response.ok) {
                    const data = await response.json();
                    addResult('获取用户信息成功', 'success', data);
                } else {
                    const errorData = await response.json();
                    addResult('获取用户信息失败', 'error', errorData);
                }
            } catch (error) {
                addResult(`获取用户信息错误: ${error.message}`, 'error');
            }
        });

        document.getElementById('testProjectsBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试项目列表...', 'info');
                const response = await apiRequest('/api/test/projects');

                if (response.ok) {
                    const data = await response.json();
                    addResult('项目列表测试成功', 'success', data);
                } else {
                    const errorData = await response.json();
                    addResult('项目列表测试失败', 'error', errorData);
                }
            } catch (error) {
                addResult(`项目列表测试错误: ${error.message}`, 'error');
            }
        });

        document.getElementById('testCreateProjectBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试创建项目...', 'info');
                const response = await apiRequest('/api/test/project', {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('创建项目测试成功', 'success', data);
                } else {
                    const errorData = await response.json();
                    addResult('创建项目测试失败', 'error', errorData);
                }
            } catch (error) {
                addResult(`创建项目测试错误: ${error.message}`, 'error');
            }
        });

        document.getElementById('testHealthBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试健康检查...', 'info');
                const response = await apiRequest('/actuator/health');

                if (response.ok) {
                    const data = await response.json();
                    addResult('健康检查成功', 'success', data);
                } else {
                    addResult('健康检查失败', 'error');
                }
            } catch (error) {
                addResult(`健康检查错误: ${error.message}`, 'error');
            }
        });

        document.getElementById('testStatusBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试系统状态...', 'info');
                const response = await apiRequest('/api/test/status');

                if (response.ok) {
                    const data = await response.json();
                    addResult('系统状态测试成功', 'success', data);
                } else {
                    const errorData = await response.json();
                    addResult('系统状态测试失败', 'error', errorData);
                }
            } catch (error) {
                addResult(`系统状态测试错误: ${error.message}`, 'error');
            }
        });

        document.getElementById('clearBtn').addEventListener('click', () => {
            document.getElementById('results').innerHTML = '<div class="text-muted text-center"><i class="fas fa-info-circle me-2"></i>结果已清空</div>';
        });

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus();
            addResult('页面加载完成，可以开始测试', 'info');
        });
    </script>
</body>
</html>
