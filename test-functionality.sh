#!/bin/bash

# 项目管理系统功能测试脚本
# 测试前端和后端的主要功能

echo "=== 项目管理系统功能测试 ==="
echo

# 设置API基础URL
API_BASE="http://localhost:8080"
FRONTEND_BASE="http://localhost:3000"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    
    echo -n "测试 $name ... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" "$url" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$url")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓ 通过${NC} (状态码: $status_code)"
        return 0
    else
        echo -e "${RED}✗ 失败${NC} (期望: $expected_status, 实际: $status_code)"
        echo "响应: $body"
        return 1
    fi
}

# 测试计数器
total_tests=0
passed_tests=0

# 1. 测试后端健康检查
echo "1. 后端服务测试"
echo "=================="

test_api "健康检查" "GET" "$API_BASE/actuator/health" "" "200"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_api "测试端点ping" "GET" "$API_BASE/api/test/ping" "" "200"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_api "服务器状态" "GET" "$API_BASE/api/test/status" "" "200"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_api "测试项目列表" "GET" "$API_BASE/api/test/projects" "" "200"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

echo

# 2. 测试用户认证功能
echo "2. 用户认证测试"
echo "=================="

# 生成随机用户名避免冲突
random_suffix=$(date +%s)
test_username="testuser$random_suffix"
test_email="test$<EMAIL>"

# 用户注册
register_data="{\"username\":\"$test_username\",\"password\":\"password123\",\"confirmPassword\":\"password123\",\"email\":\"$test_email\",\"fullName\":\"测试用户$random_suffix\"}"

test_api "用户注册" "POST" "$API_BASE/api/auth/register" "$register_data" "201"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 用户登录
login_data="{\"username\":\"$test_username\",\"password\":\"password123\"}"

echo -n "测试 用户登录 ... "
login_response=$(curl -s -X POST "$API_BASE/api/auth/login" \
    -H "Content-Type: application/json" \
    -d "$login_data")

if echo "$login_response" | grep -q "accessToken"; then
    echo -e "${GREEN}✓ 通过${NC}"
    ((passed_tests++))
    
    # 提取JWT令牌
    jwt_token=$(echo "$login_response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    echo "JWT令牌: ${jwt_token:0:20}..."
else
    echo -e "${RED}✗ 失败${NC}"
    echo "响应: $login_response"
fi
((total_tests++))

echo

# 3. 测试前端页面访问
echo "3. 前端页面测试"
echo "=================="

test_frontend_page() {
    local name="$1"
    local path="$2"
    
    echo -n "测试 $name ... "
    
    response=$(curl -s -w "%{http_code}" "$FRONTEND_BASE$path")
    status_code="${response: -3}"
    
    if [ "$status_code" = "200" ]; then
        echo -e "${GREEN}✓ 通过${NC}"
        return 0
    else
        echo -e "${RED}✗ 失败${NC} (状态码: $status_code)"
        return 1
    fi
}

test_frontend_page "主页" "/"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_frontend_page "登录页面" "/pages/login.html"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_frontend_page "项目页面" "/pages/projects.html"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_frontend_page "任务页面" "/pages/tasks.html"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

test_frontend_page "用户页面" "/pages/users.html"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

echo

# 4. 测试数据库连接（H2控制台）
echo "4. 数据库连接测试"
echo "=================="

test_api "H2控制台访问" "GET" "$API_BASE/h2-console" "" "200"
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

echo

# 5. 测试结果汇总
echo "5. 测试结果汇总"
echo "=================="

echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}所有测试通过！ 🎉${NC}"
    exit 0
else
    echo -e "${YELLOW}部分测试失败，请检查相关功能${NC}"
    exit 1
fi
