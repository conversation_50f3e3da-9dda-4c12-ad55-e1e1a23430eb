package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器
 * 用于测试前后端连接
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 简单的ping测试
     * @return 测试响应
     */
    @GetMapping("/ping")
    public ResponseEntity<ApiResponse<String>> ping() {
        return ResponseEntity.ok(ApiResponse.success("pong"));
    }

    /**
     * 获取服务器状态
     * @return 服务器状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("server", "项目管理系统后端");
        status.put("version", "1.0.0");
        status.put("timestamp", LocalDateTime.now());
        status.put("status", "running");

        return ResponseEntity.ok(ApiResponse.success("服务器状态正常", status));
    }

    /**
     * 测试项目创建（无需认证）
     * @return 测试项目数据
     */
    @PostMapping("/project")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testCreateProject() {
        Map<String, Object> project = new HashMap<>();
        project.put("id", 1L);
        project.put("name", "测试项目");
        project.put("description", "这是一个测试项目");
        project.put("status", "ACTIVE");
        project.put("createdAt", LocalDateTime.now());

        return ResponseEntity.ok(ApiResponse.success("测试项目创建成功", project));
    }

    /**
     * 获取测试项目列表（无需认证）
     * @return 测试项目列表
     */
    @GetMapping("/projects")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> testGetProjects() {
        List<Map<String, Object>> projects = new ArrayList<>();

        for (int i = 1; i <= 3; i++) {
            Map<String, Object> project = new HashMap<>();
            project.put("id", (long) i);
            project.put("name", "测试项目 " + i);
            project.put("description", "这是测试项目 " + i + " 的描述");
            project.put("status", i % 2 == 0 ? "ACTIVE" : "PLANNING");
            project.put("createdAt", LocalDateTime.now().minusDays(i));
            projects.add(project);
        }

        return ResponseEntity.ok(ApiResponse.success("获取测试项目列表成功", projects));
    }
}
