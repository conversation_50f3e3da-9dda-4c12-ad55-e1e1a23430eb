package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ApiResponse;
import com.example.projectmanagement.dto.JwtResponse;
import com.example.projectmanagement.dto.LoginRequest;
import com.example.projectmanagement.dto.UserDTO;
import com.example.projectmanagement.dto.UserRegistrationRequest;
import com.example.projectmanagement.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 认证控制器
 * 处理用户登录、注册等认证相关请求
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return JWT响应
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<JwtResponse>> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            // 验证用户凭据
            UserDTO user = userService.authenticateUser(loginRequest.getUsername(), loginRequest.getPassword());
            
            if (user == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户名或密码错误"));
            }

            // 生成JWT令牌（这里先返回模拟数据，后续需要实现JWT服务）
            JwtResponse jwtResponse = JwtResponse.builder()
                    .accessToken("mock-jwt-token-" + System.currentTimeMillis())
                    .tokenType("Bearer")
                    .expiresIn(3600L)
                    .userId(user.getId())
                    .username(user.getUsername())
                    .role(user.getRole() != null ? user.getRole().toString() : "MEMBER")
                    .build();

            return ResponseEntity.ok(ApiResponse.success("登录成功", jwtResponse));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("登录失败: " + e.getMessage()));
        }
    }

    /**
     * 用户注册
     * @param registrationRequest 注册请求
     * @return 注册成功的用户信息
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserDTO>> register(@Valid @RequestBody UserRegistrationRequest registrationRequest) {
        try {
            UserDTO userDTO = UserDTO.builder()
                    .username(registrationRequest.getUsername())
                    .email(registrationRequest.getEmail())
                    .fullName(registrationRequest.getFullName())
                    .phone(registrationRequest.getPhone())
                    .department(registrationRequest.getDepartment())
                    .position(registrationRequest.getPosition())
                    .build();

            UserDTO createdUser = userService.registerUser(userDTO, registrationRequest.getPassword());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("注册成功", createdUser));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("注册失败: " + e.getMessage()));
        }
    }

    /**
     * 用户登出
     * @return 登出结果
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout() {
        // 清除安全上下文
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok(ApiResponse.success("登出成功"));
    }

    /**
     * 刷新令牌
     * @param refreshToken 刷新令牌
     * @return 新的JWT响应
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<JwtResponse>> refreshToken(@RequestParam String refreshToken) {
        // TODO: 实现刷新令牌逻辑
        return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", null));
    }

    /**
     * 获取当前用户信息
     * @return 当前用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<UserDTO>> getCurrentUser() {
        try {
            // TODO: 从JWT中获取当前用户信息
            // 这里先返回模拟数据
            UserDTO currentUser = UserDTO.builder()
                    .id(1L)
                    .username("testuser")
                    .email("<EMAIL>")
                    .fullName("测试用户")
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", currentUser));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("获取用户信息失败: " + e.getMessage()));
        }
    }
}
