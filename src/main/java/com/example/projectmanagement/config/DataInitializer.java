package com.example.projectmanagement.config;

import com.example.projectmanagement.entity.*;
import com.example.projectmanagement.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 数据初始化器
 * 在应用启动时创建一些测试数据
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;
    private final TaskRepository taskRepository;
    private final ProjectMemberRepository projectMemberRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        if (userRepository.count() == 0) {
            initializeData();
        }
    }

    private void initializeData() {
        log.info("开始初始化测试数据...");

        // 创建测试用户
        User admin = createUser("admin", "<EMAIL>", "系统管理员", UserRole.ADMIN);
        User manager = createUser("manager", "<EMAIL>", "项目经理", UserRole.MANAGER);
        User user1 = createUser("user1", "<EMAIL>", "开发人员1", UserRole.MEMBER);
        User user2 = createUser("user2", "<EMAIL>", "开发人员2", UserRole.MEMBER);

        // 创建测试项目
        Project project1 = createProject("电商平台开发", "开发一个现代化的电商平台", admin);
        Project project2 = createProject("移动应用开发", "开发配套的移动应用", manager);
        Project project3 = createProject("数据分析系统", "构建数据分析和报告系统", manager);

        // 添加项目成员
        addProjectMember(project1, manager, ProjectRole.MANAGER);
        addProjectMember(project1, user1, ProjectRole.MEMBER);
        addProjectMember(project1, user2, ProjectRole.MEMBER);

        addProjectMember(project2, user1, ProjectRole.MEMBER);
        addProjectMember(project2, user2, ProjectRole.MEMBER);

        addProjectMember(project3, user1, ProjectRole.MEMBER);

        // 创建测试任务
        createTask("用户注册功能", "实现用户注册和验证功能", project1, user1, TaskPriority.HIGH);
        createTask("商品管理模块", "开发商品增删改查功能", project1, user2, TaskPriority.MEDIUM);
        createTask("支付集成", "集成第三方支付系统", project1, user1, TaskPriority.HIGH);

        createTask("UI设计", "设计移动应用界面", project2, user2, TaskPriority.MEDIUM);
        createTask("API对接", "对接后端API接口", project2, user1, TaskPriority.HIGH);

        createTask("数据建模", "设计数据分析模型", project3, user1, TaskPriority.LOW);

        log.info("测试数据初始化完成！");
    }

    private User createUser(String username, String email, String fullName, UserRole role) {
        User user = User.builder()
                .username(username)
                .email(email)
                .fullName(fullName)
                .password(passwordEncoder.encode("password123"))
                .role(role)
                .active(true)
                .build();
        return userRepository.save(user);
    }

    private Project createProject(String name, String description, User creator) {
        Project project = Project.builder()
                .name(name)
                .description(description)
                .creator(creator)
                .status(ProjectStatus.ACTIVE)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusMonths(6))
                .build();
        return projectRepository.save(project);
    }

    private void addProjectMember(Project project, User user, ProjectRole role) {
        ProjectMember member = ProjectMember.builder()
                .project(project)
                .user(user)
                .role(role)
                .active(true)
                .build();
        projectMemberRepository.save(member);
    }

    private Task createTask(String title, String description, Project project, User assignee, TaskPriority priority) {
        Task task = Task.builder()
                .title(title)
                .description(description)
                .project(project)
                .assignee(assignee)
                .creator(assignee) // 设置创建者
                .priority(priority)
                .status(TaskStatus.TODO)
                .estimatedHours(8) // 设置预估工时
                .dueDate(LocalDate.now().plusWeeks(2))
                .build();
        return taskRepository.save(task);
    }
}
