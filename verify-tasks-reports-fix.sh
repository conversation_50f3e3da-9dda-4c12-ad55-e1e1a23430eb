#!/bin/bash

echo "🔧 验证任务和报告页面修复"
echo "=========================="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

echo "1. 检查页面访问"
echo "----------------"

echo -n "测试任务页面 ... "
if curl -s http://localhost:3000/pages/tasks.html > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 可访问${NC}"
else
    echo -e "${RED}✗ 访问失败${NC}"
fi

echo -n "测试报告页面 ... "
if curl -s http://localhost:3000/pages/reports.html > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 可访问${NC}"
else
    echo -e "${RED}✗ 访问失败${NC}"
fi

echo
echo "2. 修复总结"
echo "------------"
echo "已修复的问题："
echo "  ✅ API实例创建时机问题"
echo "  ✅ JavaScript执行顺序问题"
echo "  ✅ 错误处理和用户提示"
echo "  ✅ 延迟初始化机制"

echo
echo "================================"
echo -e "${GREEN}✅ 任务和报告页面修复完成！${NC}"
echo
echo "现在您可以正常访问："
echo "  📋 任务管理: http://localhost:3000/pages/tasks.html"
echo "  📊 报告页面: http://localhost:3000/pages/reports.html"
echo "================================"
