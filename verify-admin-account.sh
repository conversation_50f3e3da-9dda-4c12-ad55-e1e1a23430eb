#!/bin/bash

# 管理员账号验证脚本

echo "🔐 验证管理员账号"
echo "=================="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API基础URL
API_BASE="http://localhost:8080"

echo "管理员账号信息："
echo "  用户名: admin"
echo "  密码: 123456"
echo "  角色: ADMIN"
echo

# 1. 检查后端服务状态
echo "1. 检查后端服务状态"
echo "--------------------"

echo -n "检查后端服务 ... "
if curl -s "$API_BASE/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 运行中${NC}"
else
    echo -e "${RED}✗ 未运行${NC}"
    echo "请先启动后端服务: mvn spring-boot:run"
    exit 1
fi

echo

# 2. 测试管理员登录
echo "2. 测试管理员登录"
echo "--------------------"

echo -n "测试管理员登录 ... "
login_response=$(curl -s -X POST "$API_BASE/api/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"123456"}')

if echo "$login_response" | grep -q '"success":true'; then
    echo -e "${GREEN}✓ 登录成功${NC}"
    
    # 提取用户信息
    username=$(echo "$login_response" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
    role=$(echo "$login_response" | grep -o '"role":"[^"]*"' | cut -d'"' -f4)
    userId=$(echo "$login_response" | grep -o '"userId":[0-9]*' | cut -d':' -f2)
    token=$(echo "$login_response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    
    echo "  用户ID: $userId"
    echo "  用户名: $username"
    echo "  角色: $role"
    echo "  访问令牌: ${token:0:20}..."
    
    # 保存令牌供后续测试使用
    admin_token="$token"
else
    echo -e "${RED}✗ 登录失败${NC}"
    echo "响应: $login_response"
    exit 1
fi

echo

# 3. 测试错误密码
echo "3. 测试错误密码"
echo "--------------------"

echo -n "测试错误密码 ... "
wrong_login_response=$(curl -s -X POST "$API_BASE/api/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"wrongpassword"}')

if echo "$wrong_login_response" | grep -q '"success":false'; then
    echo -e "${GREEN}✓ 正确拒绝错误密码${NC}"
else
    echo -e "${RED}✗ 错误密码被接受${NC}"
    echo "响应: $wrong_login_response"
fi

echo

# 4. 测试旧密码
echo "4. 测试旧密码"
echo "--------------------"

echo -n "测试旧密码 ... "
old_login_response=$(curl -s -X POST "$API_BASE/api/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"password123"}')

if echo "$old_login_response" | grep -q '"success":false'; then
    echo -e "${GREEN}✓ 正确拒绝旧密码${NC}"
else
    echo -e "${RED}✗ 旧密码仍然有效${NC}"
    echo "响应: $old_login_response"
fi

echo

# 5. 验证管理员权限（如果有相关API）
echo "5. 验证管理员权限"
echo "--------------------"

echo -n "检查用户列表访问权限 ... "
users_response=$(curl -s -X GET "$API_BASE/api/users" \
    -H "Authorization: Bearer $admin_token")

# 由于JWT认证可能还未完全实现，这里只是尝试访问
if echo "$users_response" | grep -q "users\|用户"; then
    echo -e "${GREEN}✓ 可以访问用户管理功能${NC}"
elif echo "$users_response" | grep -q "401\|403\|Unauthorized\|Forbidden"; then
    echo -e "${YELLOW}⚠ 需要完整的JWT认证实现${NC}"
else
    echo -e "${BLUE}ℹ API响应: ${users_response:0:50}...${NC}"
fi

echo

# 6. 检查数据库中的用户信息
echo "6. 数据库验证"
echo "--------------------"

echo "可以通过H2控制台验证用户数据："
echo "  URL: http://localhost:8080/h2-console"
echo "  JDBC URL: jdbc:h2:mem:testdb"
echo "  用户名: sa"
echo "  密码: (空)"
echo "  查询: SELECT * FROM users WHERE username = 'admin';"

echo

# 7. 前端登录测试
echo "7. 前端登录测试"
echo "--------------------"

echo "可以在前端页面测试登录："
echo "  URL: http://localhost:3000/pages/login.html"
echo "  用户名: admin"
echo "  密码: 123456"

echo

# 8. 其他测试用户
echo "8. 其他测试用户"
echo "--------------------"

echo "系统还创建了以下测试用户："
echo "  manager / password123 (项目经理)"
echo "  user1 / password123 (开发人员)"
echo "  user2 / password123 (开发人员)"

echo

# 总结
echo "================================"
echo -e "${GREEN}✅ 管理员账号验证完成！${NC}"
echo
echo "管理员账号已成功创建并可以正常使用："
echo "  👤 用户名: admin"
echo "  🔑 密码: 123456"
echo "  🛡️ 角色: ADMIN"
echo "  ✅ 状态: 活跃"
echo
echo "您现在可以使用此账号："
echo "  1. 登录前端管理界面"
echo "  2. 访问所有管理功能"
echo "  3. 管理用户和项目"
echo "  4. 查看系统报告"
echo "================================"
