2025-07-24 11:16:46 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 63161 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:16:46 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:16:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:16:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 6 JPA repository interfaces.
2025-07-24 11:16:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tom<PERSON> initialized with port(s): 8080 (http)
2025-07-24 11:16:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:16:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:16:48 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:16:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1083 ms
2025-07-24 11:16:48 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 11:16:48 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 11:16:48 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 11:16:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:16:49 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:272)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:246)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:175)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:295)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:252)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:173)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:16:49 [main] WARN  o.h.e.j.e.i.JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:272)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:246)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:175)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:295)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:252)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:173)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:16:49 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-24 11:16:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:16:50 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:44)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcConnection(ImprovedExtractionContextImpl.java:63)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcDatabaseMetaData(ImprovedExtractionContextImpl.java:70)
	at org.hibernate.tool.schema.extract.internal.InformationExtractorJdbcDatabaseMetaDataImpl.processTableResultSet(InformationExtractorJdbcDatabaseMetaDataImpl.java:64)
	at org.hibernate.tool.schema.extract.internal.AbstractInformationExtractorImpl.getTables(AbstractInformationExtractorImpl.java:565)
	at org.hibernate.tool.schema.extract.internal.DatabaseInformationImpl.getTablesInformation(DatabaseInformationImpl.java:122)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:68)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:220)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:123)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:196)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:85)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:335)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:471)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1498)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:16:50 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 0, SQLState: 08S01
2025-07-24 11:16:50 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-07-24 11:16:50 [main] ERROR o.s.o.j.LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
2025-07-24 11:16:50 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
2025-07-24 11:16:50 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-24 11:16:50 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 11:16:50 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:421)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 16 common frames omitted
Caused by: org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:112)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:37)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:113)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:99)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:71)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcConnection(ImprovedExtractionContextImpl.java:63)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcDatabaseMetaData(ImprovedExtractionContextImpl.java:70)
	at org.hibernate.tool.schema.extract.internal.InformationExtractorJdbcDatabaseMetaDataImpl.processTableResultSet(InformationExtractorJdbcDatabaseMetaDataImpl.java:64)
	at org.hibernate.tool.schema.extract.internal.AbstractInformationExtractorImpl.getTables(AbstractInformationExtractorImpl.java:565)
	at org.hibernate.tool.schema.extract.internal.DatabaseInformationImpl.getTablesInformation(DatabaseInformationImpl.java:122)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:68)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:220)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:123)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:196)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:85)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:335)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:471)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1498)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	... 20 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:44)
	... 36 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:25:25 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 68386 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:25:25 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:25:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:25:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 6 JPA repository interfaces.
2025-07-24 11:25:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 11:25:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:25:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:25:26 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:25:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 719 ms
2025-07-24 11:25:26 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 11:25:26 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 11:25:26 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 11:25:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:25:27 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:272)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:246)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:175)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:295)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:252)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:173)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:25:27 [main] WARN  o.h.e.j.e.i.JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:272)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:246)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:175)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:295)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:252)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:223)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:173)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:25:27 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-24 11:25:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:25:29 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:44)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcConnection(ImprovedExtractionContextImpl.java:63)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcDatabaseMetaData(ImprovedExtractionContextImpl.java:70)
	at org.hibernate.tool.schema.extract.internal.InformationExtractorJdbcDatabaseMetaDataImpl.processTableResultSet(InformationExtractorJdbcDatabaseMetaDataImpl.java:64)
	at org.hibernate.tool.schema.extract.internal.AbstractInformationExtractorImpl.getTables(AbstractInformationExtractorImpl.java:565)
	at org.hibernate.tool.schema.extract.internal.DatabaseInformationImpl.getTablesInformation(DatabaseInformationImpl.java:122)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:68)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:220)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:123)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:196)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:85)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:335)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:471)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1498)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:25:29 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 0, SQLState: 08S01
2025-07-24 11:25:29 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-07-24 11:25:29 [main] ERROR o.s.o.j.LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
2025-07-24 11:25:29 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
2025-07-24 11:25:29 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-24 11:25:29 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 11:25:29 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:421)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 16 common frames omitted
Caused by: org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:112)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:37)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:113)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:99)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:71)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcConnection(ImprovedExtractionContextImpl.java:63)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcDatabaseMetaData(ImprovedExtractionContextImpl.java:70)
	at org.hibernate.tool.schema.extract.internal.InformationExtractorJdbcDatabaseMetaDataImpl.processTableResultSet(InformationExtractorJdbcDatabaseMetaDataImpl.java:64)
	at org.hibernate.tool.schema.extract.internal.AbstractInformationExtractorImpl.getTables(AbstractInformationExtractorImpl.java:565)
	at org.hibernate.tool.schema.extract.internal.DatabaseInformationImpl.getTablesInformation(DatabaseInformationImpl.java:122)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:68)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:220)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:123)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:196)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:85)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:335)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:471)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1498)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	... 20 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:181)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:44)
	... 36 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 49 common frames omitted
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 52 common frames omitted
2025-07-24 11:26:35 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 69032 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:26:35 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:26:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:26:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 6 JPA repository interfaces.
2025-07-24 11:26:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 11:26:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:26:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:26:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:26:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 752 ms
2025-07-24 11:26:36 [main] ERROR o.s.b.w.e.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
2025-07-24 11:26:36 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-24 11:26:36 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-07-24 11:26:36 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 11:26:36 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:577)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4936)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1328)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1318)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:795)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1328)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1318)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:249)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:428)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:922)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 53 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	... 63 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 85 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 102 common frames omitted
Caused by: java.lang.IllegalStateException: Cannot load driver class: org.h2.Driver
	at org.springframework.util.Assert.state(Assert.java:97)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineDriverClassName(DataSourceProperties.java:175)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.initializeDataSourceBuilder(DataSourceProperties.java:125)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration.createDataSource(DataSourceConfiguration.java:48)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari.dataSource(DataSourceConfiguration.java:90)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 103 common frames omitted
2025-07-24 11:27:52 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 69949 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:27:52 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:27:52 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:27:52 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 6 JPA repository interfaces.
2025-07-24 11:27:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 11:27:53 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:27:53 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:27:53 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:27:53 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 820 ms
2025-07-24 11:27:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:27:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 11:27:53 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 11:27:53 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 11:27:53 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 11:27:53 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 11:27:53 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 11:27:53 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 11:27:53 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 11:27:53 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:27:54 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogController' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/controller/ActivityLogController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectRepository' defined in com.example.projectmanagement.repository.ProjectRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long); Reason: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'; nested exception is java.lang.IllegalArgumentException: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'
2025-07-24 11:27:54 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:27:54 [main] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 11:27:54 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:27:54 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:27:54 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:27:54 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:27:54 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:27:54 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:27:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 11:27:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 11:27:54 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-24 11:27:54 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 11:27:54 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogController' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/controller/ActivityLogController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectRepository' defined in com.example.projectmanagement.repository.ProjectRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long); Reason: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'; nested exception is java.lang.IllegalArgumentException: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectRepository' defined in com.example.projectmanagement.repository.ProjectRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long); Reason: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'; nested exception is java.lang.IllegalArgumentException: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'projectRepository' defined in com.example.projectmanagement.repository.ProjectRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long); Reason: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'; nested exception is java.lang.IllegalArgumentException: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long); Reason: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'; nested exception is java.lang.IllegalArgumentException: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:107)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$mapMethodsToQuery$1(QueryExecutorMethodInterceptor.java:95)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:195)
	at java.base/java.util.Iterator.forEachRemaining(Iterator.java:133)
	at java.base/java.util.Collections$UnmodifiableCollection$1.forEachRemaining(Collections.java:1054)
	at java.base/java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:484)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:913)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:578)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:97)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:87)
	at java.base/java.util.Optional.map(Optional.java:265)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:87)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:365)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:231)
	at org.springframework.data.util.Lazy.get(Lazy.java:115)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 44 common frames omitted
Caused by: java.lang.IllegalArgumentException: Failed to create query for method public abstract long com.example.projectmanagement.repository.ProjectRepository.countByUserId(java.lang.Long)! No property 'userId' found for type 'Project'
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:96)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:119)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:259)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:93)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:103)
	... 66 common frames omitted
Caused by: org.springframework.data.mapping.PropertyReferenceException: No property 'userId' found for type 'Project'
	at org.springframework.data.mapping.PropertyPath.<init>(PropertyPath.java:91)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:438)
	at org.springframework.data.mapping.PropertyPath.create(PropertyPath.java:414)
	at org.springframework.data.mapping.PropertyPath.lambda$from$0(PropertyPath.java:367)
	at java.base/java.util.concurrent.ConcurrentMap.computeIfAbsent(ConcurrentMap.java:330)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:349)
	at org.springframework.data.mapping.PropertyPath.from(PropertyPath.java:332)
	at org.springframework.data.repository.query.parser.Part.<init>(Part.java:81)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.lambda$new$0(PartTree.java:250)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:195)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:177)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:948)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:484)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:913)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:578)
	at org.springframework.data.repository.query.parser.PartTree$OrPart.<init>(PartTree.java:251)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.lambda$new$0(PartTree.java:384)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:195)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:177)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:948)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:484)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:913)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:578)
	at org.springframework.data.repository.query.parser.PartTree$Predicate.<init>(PartTree.java:385)
	at org.springframework.data.repository.query.parser.PartTree.<init>(PartTree.java:96)
	at org.springframework.data.jpa.repository.query.PartTreeJpaQuery.<init>(PartTreeJpaQuery.java:89)
	... 70 common frames omitted
2025-07-24 11:29:07 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 70721 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:29:07 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:29:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:29:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 6 JPA repository interfaces.
2025-07-24 11:29:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 11:29:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:29:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:29:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:29:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 788 ms
2025-07-24 11:29:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:29:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 11:29:07 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 11:29:08 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 11:29:08 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 11:29:08 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 11:29:08 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 11:29:08 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 11:29:08 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:29:08 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogController' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/controller/ActivityLogController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'approvalRepository' defined in com.example.projectmanagement.repository.ApprovalRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!; nested exception is java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!
2025-07-24 11:29:08 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:29:08 [main] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:29:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:29:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 11:29:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 11:29:08 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-24 11:29:08 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 11:29:08 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogController' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/controller/ActivityLogController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'approvalRepository' defined in com.example.projectmanagement.repository.ApprovalRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!; nested exception is java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'approvalRepository' defined in com.example.projectmanagement.repository.ApprovalRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!; nested exception is java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'approvalRepository' defined in com.example.projectmanagement.repository.ApprovalRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Invocation of init method failed; nested exception is org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!; nested exception is java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!; nested exception is java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:107)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$mapMethodsToQuery$1(QueryExecutorMethodInterceptor.java:95)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:195)
	at java.base/java.util.Iterator.forEachRemaining(Iterator.java:133)
	at java.base/java.util.Collections$UnmodifiableCollection$1.forEachRemaining(Collections.java:1054)
	at java.base/java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:484)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:913)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:578)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:97)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:87)
	at java.base/java.util.Optional.map(Optional.java:265)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:87)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:365)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:323)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:231)
	at org.springframework.data.util.Lazy.get(Lazy.java:115)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:329)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:144)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 44 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.example.projectmanagement.repository.ApprovalRepository.findByFilters(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,com.example.projectmanagement.entity.ApprovalStatus,com.example.projectmanagement.entity.ApprovalType,java.time.LocalDateTime,java.time.LocalDateTime,java.lang.String,org.springframework.data.domain.Pageable)!
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:96)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:66)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:51)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:169)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:253)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:93)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:103)
	... 66 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.QueryException: could not resolve property: requesterId of: com.example.projectmanagement.entity.Approval [SELECT a FROM com.example.projectmanagement.entity.Approval a WHERE (:requesterId IS NULL OR a.requesterId = :requesterId) AND (:reviewerId IS NULL OR a.reviewerId = :reviewerId) AND (:approverId IS NULL OR a.approverId = :approverId) AND (:projectId IS NULL OR a.projectId = :projectId) AND (:status IS NULL OR a.status = :status) AND (:type IS NULL OR a.type = :type) AND (:startDate IS NULL OR a.createdAt >= :startDate) AND (:endDate IS NULL OR a.createdAt <= :endDate) AND (:keyword IS NULL OR LOWER(a.title) LIKE LOWER(CONCAT('%', :keyword, '%')))]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:138)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:181)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:188)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:757)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:114)
	at jdk.internal.reflect.GeneratedMethodAccessor24.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:362)
	at com.sun.proxy.$Proxy145.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:90)
	... 72 common frames omitted
Caused by: org.hibernate.QueryException: could not resolve property: requesterId of: com.example.projectmanagement.entity.Approval [SELECT a FROM com.example.projectmanagement.entity.Approval a WHERE (:requesterId IS NULL OR a.requesterId = :requesterId) AND (:reviewerId IS NULL OR a.reviewerId = :reviewerId) AND (:approverId IS NULL OR a.approverId = :approverId) AND (:projectId IS NULL OR a.projectId = :projectId) AND (:status IS NULL OR a.status = :status) AND (:type IS NULL OR a.type = :type) AND (:startDate IS NULL OR a.createdAt >= :startDate) AND (:endDate IS NULL OR a.createdAt <= :endDate) AND (:keyword IS NULL OR LOWER(a.title) LIKE LOWER(CONCAT('%', :keyword, '%')))]
	at org.hibernate.QueryException.generateQueryException(QueryException.java:120)
	at org.hibernate.QueryException.wrapWithQueryString(QueryException.java:103)
	at org.hibernate.hql.internal.ast.QueryTranslatorImpl.doCompile(QueryTranslatorImpl.java:220)
	at org.hibernate.hql.internal.ast.QueryTranslatorImpl.compile(QueryTranslatorImpl.java:144)
	at org.hibernate.engine.query.spi.HQLQueryPlan.<init>(HQLQueryPlan.java:112)
	at org.hibernate.engine.query.spi.HQLQueryPlan.<init>(HQLQueryPlan.java:73)
	at org.hibernate.engine.query.spi.QueryPlanCache.getHQLQueryPlan(QueryPlanCache.java:162)
	at org.hibernate.internal.AbstractSharedSessionContract.getQueryPlan(AbstractSharedSessionContract.java:636)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:748)
	... 79 common frames omitted
Caused by: org.hibernate.QueryException: could not resolve property: requesterId of: com.example.projectmanagement.entity.Approval
	at org.hibernate.persister.entity.AbstractPropertyMapping.propertyException(AbstractPropertyMapping.java:78)
	at org.hibernate.persister.entity.AbstractPropertyMapping.toType(AbstractPropertyMapping.java:72)
	at org.hibernate.persister.entity.AbstractEntityPersister.toType(AbstractEntityPersister.java:2053)
	at org.hibernate.hql.internal.ast.tree.FromElementType.getPropertyType(FromElementType.java:412)
	at org.hibernate.hql.internal.ast.tree.FromElement.getPropertyType(FromElement.java:524)
	at org.hibernate.hql.internal.ast.tree.DotNode.getDataType(DotNode.java:723)
	at org.hibernate.hql.internal.ast.tree.DotNode.prepareLhs(DotNode.java:268)
	at org.hibernate.hql.internal.ast.tree.DotNode.resolve(DotNode.java:208)
	at org.hibernate.hql.internal.ast.HqlSqlWalker.resolve(HqlSqlWalker.java:1066)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.expr(HqlSqlBaseWalker.java:1319)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.exprOrSubquery(HqlSqlBaseWalker.java:4797)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.comparisonExpr(HqlSqlBaseWalker.java:4261)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2180)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2133)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.logicalExpr(HqlSqlBaseWalker.java:2105)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.whereClause(HqlSqlBaseWalker.java:841)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.query(HqlSqlBaseWalker.java:635)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.selectStatement(HqlSqlBaseWalker.java:339)
	at org.hibernate.hql.internal.antlr.HqlSqlBaseWalker.statement(HqlSqlBaseWalker.java:287)
	at org.hibernate.hql.internal.ast.QueryTranslatorImpl.analyze(QueryTranslatorImpl.java:276)
	at org.hibernate.hql.internal.ast.QueryTranslatorImpl.doCompile(QueryTranslatorImpl.java:192)
	... 85 common frames omitted
2025-07-24 11:30:55 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 71766 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:30:55 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:30:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:30:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 6 JPA repository interfaces.
2025-07-24 11:30:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 11:30:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:30:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:30:56 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:30:56 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 726 ms
2025-07-24 11:30:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:30:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 11:30:56 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 11:30:56 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 11:30:56 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 11:30:56 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 11:30:56 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 11:30:57 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 11:30:57 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:30:57 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogController' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/controller/ActivityLogController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'activityLogServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ActivityLogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 5; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'projectServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/ProjectServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 4; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl' defined in file [/Users/<USER>/ai/fs2/target/classes/com/example/projectmanagement/service/impl/UserServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.security.crypto.password.PasswordEncoder' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-24 11:30:57 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:30:57 [main] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:30:57 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:30:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 11:30:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 11:30:57 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-24 11:30:57 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 11:30:57 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 2 of constructor in com.example.projectmanagement.service.impl.UserServiceImpl required a bean of type 'org.springframework.security.crypto.password.PasswordEncoder' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.security.crypto.password.PasswordEncoder' in your configuration.

2025-07-24 11:32:16 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 72664 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 11:32:16 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 11:32:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 11:32:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 6 JPA repository interfaces.
2025-07-24 11:32:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 11:32:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 11:32:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 11:32:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 11:32:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 763 ms
2025-07-24 11:32:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 11:32:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 11:32:17 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 11:32:17 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 11:32:17 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 11:32:17 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 11:32:17 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 11:32:17 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 11:32:17 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 11:32:17 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 11:32:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@20cf3ab3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1acfc058, org.springframework.security.web.context.SecurityContextPersistenceFilter@1659d7d, org.springframework.security.web.header.HeaderWriterFilter@222a7429, org.springframework.security.web.authentication.logout.LogoutFilter@df04d12, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3fb42ec7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5248c05a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@725490dd, org.springframework.security.web.session.SessionManagementFilter@5ae67f05, org.springframework.security.web.access.ExceptionTranslationFilter@85eba52, org.springframework.security.web.access.intercept.AuthorizationFilter@390978]
2025-07-24 11:32:18 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 11:32:18 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 11:32:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 11:32:18 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.549 seconds (JVM running for 7.741)
2025-07-24 11:32:55 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 11:32:55 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 11:32:55 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-24 11:32:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 11:32:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:32:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:32:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 11:32:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:07 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console
2025-07-24 11:33:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:07 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console
2025-07-24 11:33:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:33:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:33:21 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:28 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 11:33:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:28 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 11:33:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:33:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:33 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console
2025-07-24 11:33:33 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console
2025-07-24 11:33:33 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:33 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/
2025-07-24 11:33:33 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/
2025-07-24 11:33:33 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:33 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 11:33:33 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 11:33:33 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:33 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/login.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/login.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:33 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/background.gif
2025-07-24 11:33:33 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:33 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/background.gif
2025-07-24 11:33:33 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:33:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:36 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/index.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16&language=zh_CN
2025-07-24 11:59:36 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/index.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16&language=zh_CN
2025-07-24 11:59:36 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:36 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 11:59:36 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 11:59:36 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:36 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/login.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:36 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/login.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:36 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:59:36 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:39 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:39 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:39 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:39 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:39 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:59:39 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:50 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:50 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:50 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:50 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 11:59:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 11:59:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:59:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:51 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:51 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:51 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:59:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:57 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:57 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:57 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:57 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 11:59:57 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 11:59:57 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:00 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/admin.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:00 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/admin.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:00 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:00 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 12:00:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 12:00:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:09 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/adminLogin.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:09 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:09 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:09 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/adminLogin.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:09 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:09 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:31 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/index.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:31 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/index.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:31 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:31 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 12:00:31 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 12:00:31 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:31 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/login.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:31 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/login.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:31 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:31 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:36 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:36 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:36 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:36 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:36 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:36 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:38 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:38 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:38 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:38 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:38 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:38 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:40 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:40 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:40 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:40 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:40 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:40 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 12:00:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 12:00:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:00:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:00:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:01:17 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:03:21 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:03:21 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:03:21 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:03:21 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 12:03:21 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 12:03:21 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:03:21 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:04:10 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:10 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:04:10 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:40 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:40 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/test.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:40 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/stylesheet.css
2025-07-24 12:04:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/stylesheet.css
2025-07-24 12:04:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:04:40 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /h2-console/login.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/header.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/header.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tables.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tables.do?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/help.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/query.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/help.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/query.jsp?jsessionid=e9ea41e3ec07184c62f99a90824acd16
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_refresh.gif
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_disconnect.gif
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_disconnect.gif
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_refresh.gif
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_line.gif
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_line.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_history.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_history.gif
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_run_selected.gif
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_run_selected.gif
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_stop.gif
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_stop.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_help.gif
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_help.gif
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_commit.gif
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_rollback.gif
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/icon_run.gif
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_commit.gif
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_rollback.gif
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/icon_run.gif
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree.js
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree.js
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_plus.gif
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_database.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_minus.gif
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_table.gif
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_empty.gif
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_table.gif
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_database.gif
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_plus.gif
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_empty.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_column.gif
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_minus.gif
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_column.gif
2025-07-24 12:04:41 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_type.gif
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_view.gif
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_view.gif
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_users.gif
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_index.gif
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_folder.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_index_az.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_index_az.gif
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_index.gif
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_users.gif
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_type.gif
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_folder.gif
2025-07-24 12:04:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:41 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_user.gif
2025-07-24 12:04:42 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console/tree_info.gif
2025-07-24 12:04:42 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_user.gif
2025-07-24 12:04:42 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console/tree_info.gif
2025-07-24 12:04:42 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:42 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:04:42 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:08:50 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:08:50 [SpringApplicationShutdownHook] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:08:50 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:08:50 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:08:50 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:08:50 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:08:50 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:08:50 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:08:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:08:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:09:14 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 90301 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:09:14 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:09:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:09:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 6 JPA repository interfaces.
2025-07-24 12:09:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:09:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:09:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:09:15 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:09:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 742 ms
2025-07-24 12:09:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:09:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:09:15 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:09:15 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:09:15 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:09:15 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:09:15 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:09:15 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:09:15 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:09:15 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:09:16 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5325674a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6a0aa5e2, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d12fb85, org.springframework.security.web.header.HeaderWriterFilter@561c031, org.springframework.web.filter.CorsFilter@630bd5e7, org.springframework.security.web.authentication.logout.LogoutFilter@63c66980, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@470ad01c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3fc2a1d1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2389f546, org.springframework.security.web.session.SessionManagementFilter@3af6c672, org.springframework.security.web.access.ExceptionTranslationFilter@309d54ac, org.springframework.security.web.access.intercept.AuthorizationFilter@429dde4d]
2025-07-24 12:09:16 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:09:16 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:09:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:09:16 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.549 seconds (JVM running for 7.718)
2025-07-24 12:10:06 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 12:10:06 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 12:10:06 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-24 12:10:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/
2025-07-24 12:10:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:10:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:10:14 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:10:14 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:10:14 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:10:14 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:10:14 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:11:22 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:11:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:11:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:11:22 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:11:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:11:24 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/
2025-07-24 12:11:24 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:11:24 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:11:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:12:14 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:12:14 [SpringApplicationShutdownHook] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:12:14 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:12:14 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:12:14 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:12:14 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:12:14 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:12:14 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:12:14 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:12:14 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:12:44 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 92423 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:12:44 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:12:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:12:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 6 JPA repository interfaces.
2025-07-24 12:12:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:12:44 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:12:44 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:12:44 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:12:44 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 739 ms
2025-07-24 12:12:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:12:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:12:44 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:12:45 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:12:45 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:12:45 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:12:45 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:12:45 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:12:45 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:12:45 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:12:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@58b30e3e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5325674a, org.springframework.security.web.context.SecurityContextPersistenceFilter@497c1d78, org.springframework.security.web.header.HeaderWriterFilter@390978, org.springframework.web.filter.CorsFilter@6a0aa5e2, org.springframework.security.web.authentication.logout.LogoutFilter@9771c71, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@71db6703, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e590e36, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@630bd5e7, org.springframework.security.web.session.SessionManagementFilter@38318d67, org.springframework.security.web.access.ExceptionTranslationFilter@70b0dc92, org.springframework.security.web.access.intercept.AuthorizationFilter@4d529bc0]
2025-07-24 12:12:46 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:12:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:12:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:12:46 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.537 seconds (JVM running for 7.71)
2025-07-24 12:13:07 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 12:13:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 12:13:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-24 12:13:07 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:13:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:07 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:13:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:13:09 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:13:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:13:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /error
2025-07-24 12:13:13 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:14 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:13:14 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:14 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:14 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:13:14 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:26 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/ping
2025-07-24 12:13:26 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:26 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:26 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/ping
2025-07-24 12:13:26 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:13:37 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/status
2025-07-24 12:13:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:13:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:13:37 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/status
2025-07-24 12:13:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:14:36 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /actuator/health
2025-07-24 12:14:36 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:14:36 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:14:36 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /actuator/health
2025-07-24 12:14:36 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:14:37 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/test/ping
2025-07-24 12:14:37 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:14:37 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:14:37 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/ping
2025-07-24 12:14:37 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:14:37 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:14:37 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/ping
2025-07-24 12:14:37 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:14:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/test/status
2025-07-24 12:14:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:14:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:14:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/status
2025-07-24 12:14:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:14:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:14:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/status
2025-07-24 12:14:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:17:09 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:17:09 [SpringApplicationShutdownHook] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:17:09 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:17:09 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:17:09 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:17:09 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:17:09 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:17:09 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:17:09 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:17:09 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:18:15 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 95477 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:18:15 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:18:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:18:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 6 JPA repository interfaces.
2025-07-24 12:18:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:18:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:18:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:18:16 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:18:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 785 ms
2025-07-24 12:18:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:18:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:18:16 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:18:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:18:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:18:16 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:18:16 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:18:16 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:18:16 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:18:16 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:18:17 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6a0aa5e2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@630bd5e7, org.springframework.security.web.context.SecurityContextPersistenceFilter@71db6703, org.springframework.security.web.header.HeaderWriterFilter@7343d843, org.springframework.web.filter.CorsFilter@2389f546, org.springframework.security.web.authentication.logout.LogoutFilter@8cce9ad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e349258, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@752e4223, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5b6ec94a, org.springframework.security.web.session.SessionManagementFilter@12948e7a, org.springframework.security.web.access.ExceptionTranslationFilter@390978, org.springframework.security.web.access.intercept.AuthorizationFilter@610d0067]
2025-07-24 12:18:17 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:18:17 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:18:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:18:17 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.604 seconds (JVM running for 7.782)
2025-07-24 12:18:59 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 12:18:59 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 12:18:59 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/register
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/register
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0.id 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as col_0_0_ 
        from
            users user0_ 
        where
            user0_.username=? limit ?
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [testuser]
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0.id 
    from
        User as generatedAlias0 
    where
        generatedAlias0.email=:param0 */ select
            user0_.id as col_0_0_ 
        from
            users user0_ 
        where
            user0_.email=? limit ?
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:18:59.547235]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [测试用户]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$YBtL/NJJMCY12mqvBxzaeuKkPZFvvUjAUXtcoy8pZzIsYxib.QJdi]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:18:59.547275]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [testuser]
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_REGISTER]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:18:59.556752]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 testuser 注册成功]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [127.0.0.1]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 12:18:59 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [1]
2025-07-24 12:18:59 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as id1_5_,
            user0_.active as active2_5_,
            user0_.avatar as avatar3_5_,
            user0_.createdAt as createda4_5_,
            user0_.department as departme5_5_,
            user0_.email as email6_5_,
            user0_.fullName as fullname7_5_,
            user0_.password as password8_5_,
            user0_.phone as phone9_5_,
            user0_.position as positio10_5_,
            user0_.role as role11_5_,
            user0_.updatedAt as updated12_5_,
            user0_.username as usernam13_5_ 
        from
            users user0_ 
        where
            user0_.username=?
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [testuser]
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_LOGIN]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:19:08.239259]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 testuser 登录成功]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [127.0.0.1]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 12:19:08 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [1]
2025-07-24 12:19:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:19:58 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-07-24 12:19:58 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:19:58 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as id1_5_,
            user0_.active as active2_5_,
            user0_.avatar as avatar3_5_,
            user0_.createdAt as createda4_5_,
            user0_.department as departme5_5_,
            user0_.email as email6_5_,
            user0_.fullName as fullname7_5_,
            user0_.password as password8_5_,
            user0_.phone as phone9_5_,
            user0_.position as positio10_5_,
            user0_.role as role11_5_,
            user0_.updatedAt as updated12_5_,
            user0_.username as usernam13_5_ 
        from
            users user0_ 
        where
            user0_.username=?
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [testuser]
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_LOGIN]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:19:58.346861]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 testuser 登录成功]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [127.0.0.1]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 12:19:58 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [1]
2025-07-24 12:19:58 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:19:59 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/register
2025-07-24 12:19:59 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:19:59 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/register
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/register
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0.id 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as col_0_0_ 
        from
            users user0_ 
        where
            user0_.username=? limit ?
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [testuser1753330799211]
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0.id 
    from
        User as generatedAlias0 
    where
        generatedAlias0.email=:param0 */ select
            user0_.id as col_0_0_ 
        from
            users user0_ 
        where
            user0_.email=? limit ?
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:19:59.313373]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [测试用户]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$k/bETsTuPyskXZ4fNuE.NOyk0Wkwqth3RrCI.MmXKYmmxAy5eDoM.]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:19:59.313396]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [testuser1753330799211]
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_REGISTER]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:19:59.314839]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 testuser1753330799211 注册成功]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [0:0:0:0:0:0:0:1]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 12:19:59 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [2]
2025-07-24 12:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:20:43 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:20:43 [SpringApplicationShutdownHook] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:20:43 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:20:43 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:20:43 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:20:43 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:20:43 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:20:43 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:20:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:20:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:21:07 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 97504 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:21:07 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:21:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:21:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 6 JPA repository interfaces.
2025-07-24 12:21:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:21:08 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:21:08 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:21:08 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:21:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 754 ms
2025-07-24 12:21:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:21:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:21:08 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:21:08 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:21:08 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:21:08 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:21:08 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:21:08 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:21:09 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:21:09 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:21:09 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:21:09 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@35a2ea5a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18ab513d, org.springframework.security.web.context.SecurityContextPersistenceFilter@752e4223, org.springframework.security.web.header.HeaderWriterFilter@1acfc058, org.springframework.web.filter.CorsFilter@5e8a678a, org.springframework.security.web.authentication.logout.LogoutFilter@7a023e34, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@136690b1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c257c0a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6fe91918, org.springframework.security.web.session.SessionManagementFilter@41092c8, org.springframework.security.web.access.ExceptionTranslationFilter@71c1ca1, org.springframework.security.web.access.intercept.AuthorizationFilter@5325674a]
2025-07-24 12:21:09 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:21:09 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:21:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:21:09 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.535 seconds (JVM running for 7.711)
2025-07-24 12:21:48 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 12:21:48 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 12:21:48 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-24 12:21:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/projects
2025-07-24 12:21:48 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:21:48 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:21:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/projects
2025-07-24 12:21:48 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:24:11 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /h2-console
2025-07-24 12:24:11 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:24:11 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:24:11 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /h2-console
2025-07-24 12:24:11 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:24:44 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:24:44 [SpringApplicationShutdownHook] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:24:44 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:24:44 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:24:44 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:24:44 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:24:44 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:24:44 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:24:44 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:24:44 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:26:21 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 726 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:26:21 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:26:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:26:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 6 JPA repository interfaces.
2025-07-24 12:26:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:26:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:26:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:26:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:26:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 744 ms
2025-07-24 12:26:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:26:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:26:22 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:26:22 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:26:22 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:26:22 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:26:22 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:26:23 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:26:23 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:26:23 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:26:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@55a5eea3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46ab8e4b, org.springframework.security.web.context.SecurityContextPersistenceFilter@35975b05, org.springframework.security.web.header.HeaderWriterFilter@16269ff4, org.springframework.web.filter.CorsFilter@66428512, org.springframework.security.web.authentication.logout.LogoutFilter@5e8a678a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6ba02f70, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63c66980, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e21d73, org.springframework.security.web.session.SessionManagementFilter@b1f36e5, org.springframework.security.web.access.ExceptionTranslationFilter@be186df, org.springframework.security.web.access.intercept.AuthorizationFilter@256d8f17]
2025-07-24 12:26:23 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:26:24 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:26:24 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:26:24 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.595 seconds (JVM running for 7.769)
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        User x */ select
            count(*) as col_0_0_ 
        from
            users user0_
2025-07-24 12:26:24 [main] INFO  c.e.p.config.DataInitializer - 开始初始化测试数据...
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:26:24.331263]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [系统管理员]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$4CdaZ0EFxVWrysLnCk8zc.05gsI1HRhLYwyaz8KFo/EZkqnu0FiHW]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [ADMIN]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:26:24.331290]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [admin]
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:26:24.432594]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [项目经理]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$hjgtbIyH5.sUSjU/HgI/EOuqZcYv/.nuSN1kI92y/yp94EZRy.ZZm]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MANAGER]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:26:24.432608]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [manager]
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:26:24.525230]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发人员1]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$nm1ze27Zme2g2tygMqgkXOeJe8sSwHgTGqYbGcNlU4wbOfF662B7m]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:26:24.525244]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [user1]
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:26:24.617979]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发人员2]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$njSe706EW8ionbhGs0OwlujPPXflHgcXngfXH1p2qOkKXZ.a8KEyq]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:26:24.617994]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [user2]
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:26:24.620292]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [null]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [开发一个现代化的电商平台]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [电商平台开发]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:26:24 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:26:24.620300]
2025-07-24 12:26:24 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 23502, SQLState: 23502
2025-07-24 12:26:24 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - NULL not allowed for column "CREATOR_ID"; SQL statement:
/* insert com.example.projectmanagement.entity.Project */ insert into projects (id, archived, createdAt, creator_id, description, endDate, name, startDate, stat [23502-214]
2025-07-24 12:26:24 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 12:26:24 [main] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: Failed to execute CommandLineRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement; SQL [n/a]; constraint [null]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:276)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:233)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy148.save(Unknown Source)
	at com.example.projectmanagement.config.DataInitializer.createProject(DataInitializer.java:93)
	at com.example.projectmanagement.config.DataInitializer.initializeData(DataInitializer.java:46)
	at com.example.projectmanagement.config.DataInitializer.run(DataInitializer.java:32)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	... 5 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:59)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:37)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:113)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:99)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:200)
	at org.hibernate.dialect.identity.GetGeneratedKeysDelegate.executeAndExtract(GetGeneratedKeysDelegate.java:58)
	at org.hibernate.id.insert.AbstractReturningDelegate.performInsert(AbstractReturningDelegate.java:43)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3279)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3914)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:84)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:645)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:282)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:263)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:317)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:329)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:286)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:192)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:122)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:185)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:128)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:55)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:107)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:756)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:742)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:315)
	at com.sun.proxy.$Proxy142.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:666)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:76)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 16 common frames omitted
Caused by: org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: NULL not allowed for column "CREATOR_ID"; SQL statement:
/* insert com.example.projectmanagement.entity.Project */ insert into projects (id, archived, createdAt, creator_id, description, endDate, name, startDate, stat [23502-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:508)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:365)
	at org.h2.table.Table.convertInsertRow(Table.java:926)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.CommandContainer.executeUpdateWithGeneratedKeys(CommandContainer.java:242)
	at org.h2.command.CommandContainer.update(CommandContainer.java:163)
	at org.h2.command.Command.executeUpdate(Command.java:252)
	at org.h2.jdbc.JdbcPreparedStatement.executeUpdateInternal(JdbcPreparedStatement.java:209)
	at org.h2.jdbc.JdbcPreparedStatement.executeUpdate(JdbcPreparedStatement.java:169)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	... 63 common frames omitted
2025-07-24 12:26:24 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:26:24 [main] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:26:24 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:26:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:26:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:27:19 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 1372 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:27:19 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:27:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:27:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 6 JPA repository interfaces.
2025-07-24 12:27:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:27:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:27:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:27:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:27:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 759 ms
2025-07-24 12:27:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:27:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:27:20 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:27:20 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:27:20 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:27:20 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:27:20 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:27:20 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:27:20 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:27:20 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:27:21 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@66428512, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@e21d73, org.springframework.security.web.context.SecurityContextPersistenceFilter@6ba02f70, org.springframework.security.web.header.HeaderWriterFilter@6f9b5f01, org.springframework.web.filter.CorsFilter@7a482b3a, org.springframework.security.web.authentication.logout.LogoutFilter@4d3bde85, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2148849f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6dc5e857, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@600b3bee, org.springframework.security.web.session.SessionManagementFilter@397f925e, org.springframework.security.web.access.ExceptionTranslationFilter@16269ff4, org.springframework.security.web.access.intercept.AuthorizationFilter@659e003e]
2025-07-24 12:27:21 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:27:21 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:27:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:27:21 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.542 seconds (JVM running for 7.712)
2025-07-24 12:27:21 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        User x */ select
            count(*) as col_0_0_ 
        from
            users user0_
2025-07-24 12:27:21 [main] INFO  c.e.p.config.DataInitializer - 开始初始化测试数据...
2025-07-24 12:27:21 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:27:21.788067]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [系统管理员]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$Sn3nk2Xg9TBJihsk2SXTJ.H/knmsqRDNDzQuuQFRFDKTd.Og5ret.]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [ADMIN]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:27:21.788093]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [admin]
2025-07-24 12:27:21 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:27:21.888767]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [项目经理]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$g.gDbVflpeMe9YAngf8mY.lkH98rniz13w3GUxdM93z8bkSeh1.N6]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MANAGER]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:27:21.888786]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [manager]
2025-07-24 12:27:21 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:27:21.981911]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发人员1]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$bda8mBTg60geChW6qtwkA.bklDyR2D2FOtBqXt1zNTJ.Weg6qHEla]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:27:21.981926]
2025-07-24 12:27:21 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [user1]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:27:22.073852]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发人员2]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$PuY9ls22FuDnPomXxxh4g.SZ7Z3DqzD6hR2Bk5ZgjTu2u1cu/PvCK]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:27:22.073867]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [user2]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.076522]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [开发一个现代化的电商平台]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [电商平台开发]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:27:22.076531]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.078531]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [开发配套的移动应用]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [移动应用开发]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:27:22.078539]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.079944]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [构建数据分析和报告系统]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [数据分析系统]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:27:22.079952]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.081628]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MANAGER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:27:22.081637]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [2]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.083244]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:27:22.083253]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [3]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.084051]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:27:22.084057]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [4]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.085031]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:27:22.085042]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [3]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.086026]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:27:22.086034]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [4]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:27:22.086812]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:27:22.086818]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [3]
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:27:22.088478]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [实现用户注册和验证功能]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [null]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [HIGH]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [1]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [用户注册功能]
2025-07-24 12:27:22 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:27:22.088487]
2025-07-24 12:27:22 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 23502, SQLState: 23502
2025-07-24 12:27:22 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - NULL not allowed for column "ESTIMATEDHOURS"; SQL statement:
/* insert com.example.projectmanagement.entity.Task */ insert into tasks (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt)  [23502-214]
2025-07-24 12:27:22 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-24 12:27:22 [main] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: Failed to execute CommandLineRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.example.projectmanagement.ProjectManagementApplication.main(ProjectManagementApplication.java:14)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement; SQL [n/a]; constraint [null]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:276)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:233)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:551)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:242)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy149.save(Unknown Source)
	at com.example.projectmanagement.config.DataInitializer.createTask(DataInitializer.java:117)
	at com.example.projectmanagement.config.DataInitializer.initializeData(DataInitializer.java:61)
	at com.example.projectmanagement.config.DataInitializer.run(DataInitializer.java:32)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	... 5 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:59)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:37)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:113)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:99)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:200)
	at org.hibernate.dialect.identity.GetGeneratedKeysDelegate.executeAndExtract(GetGeneratedKeysDelegate.java:58)
	at org.hibernate.id.insert.AbstractReturningDelegate.performInsert(AbstractReturningDelegate.java:43)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3279)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:3914)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:84)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:645)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:282)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:263)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:317)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:329)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:286)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:192)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:122)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:185)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:128)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:55)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:107)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:756)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:742)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:315)
	at com.sun.proxy.$Proxy142.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:666)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:76)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 16 common frames omitted
Caused by: org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: NULL not allowed for column "ESTIMATEDHOURS"; SQL statement:
/* insert com.example.projectmanagement.entity.Task */ insert into tasks (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt)  [23502-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:508)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:365)
	at org.h2.table.Table.convertInsertRow(Table.java:926)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.CommandContainer.executeUpdateWithGeneratedKeys(CommandContainer.java:242)
	at org.h2.command.CommandContainer.update(CommandContainer.java:163)
	at org.h2.command.Command.executeUpdate(Command.java:252)
	at org.h2.jdbc.JdbcPreparedStatement.executeUpdateInternal(JdbcPreparedStatement.java:209)
	at org.h2.jdbc.JdbcPreparedStatement.executeUpdate(JdbcPreparedStatement.java:169)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	... 63 common frames omitted
2025-07-24 12:27:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:27:22 [main] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:27:22 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:27:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 12:27:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-24 12:29:00 [main] INFO  c.e.p.ProjectManagementApplication - Starting ProjectManagementApplication using Java 11.0.27 on xutongdeMacBook-Pro.local with PID 2358 (/Users/<USER>/ai/fs2/target/classes started by xutong in /Users/<USER>/ai/fs2)
2025-07-24 12:29:00 [main] INFO  c.e.p.ProjectManagementApplication - The following 1 profile is active: "dev"
2025-07-24 12:29:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 12:29:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 6 JPA repository interfaces.
2025-07-24 12:29:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-24 12:29:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 12:29:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-24 12:29:01 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 12:29:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 782 ms
2025-07-24 12:29:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 12:29:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 12:29:01 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-24 12:29:01 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 12:29:01 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-24 12:29:01 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 12:29:01 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    create table activity_logs (
       id bigint generated by default as identity,
        activityType varchar(255) not null,
        createdAt timestamp not null,
        description varchar(1000) not null,
        ipAddress varchar(255) not null,
        approval_id bigint,
        project_id bigint,
        task_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    create table approvals (
       id bigint generated by default as identity,
        approvalTime timestamp,
        comment varchar(1000),
        comments varchar(1000),
        createdAt timestamp not null,
        description varchar(1000),
        reviewedAt timestamp,
        status varchar(255) not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updatedAt timestamp not null,
        approver_id bigint,
        project_id bigint not null,
        requester_id bigint not null,
        reviewer_id bigint,
        task_id bigint,
        primary key (id)
    )
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    create table project_members (
       id bigint generated by default as identity,
        active boolean not null,
        joinedAt timestamp not null,
        role varchar(255) not null,
        updatedAt timestamp not null,
        project_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    create table projects (
       id bigint generated by default as identity,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        endDate date,
        name varchar(255) not null,
        startDate date,
        status varchar(255) not null,
        updatedAt timestamp not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    create table tasks (
       id bigint generated by default as identity,
        actualHours integer,
        archived boolean not null,
        createdAt timestamp not null,
        description varchar(1000),
        dueDate date,
        estimatedHours integer not null,
        priority varchar(255) not null,
        status varchar(255) not null,
        title varchar(255) not null,
        updatedAt timestamp not null,
        assignee_id bigint,
        creator_id bigint not null,
        project_id bigint not null,
        primary key (id)
    )
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    create table users (
       id bigint generated by default as identity,
        active boolean not null,
        avatar varchar(255),
        createdAt timestamp not null,
        department varchar(255),
        email varchar(255) not null,
        fullName varchar(255),
        password varchar(255) not null,
        phone varchar(255),
        position varchar(255),
        role varchar(255) not null,
        updatedAt timestamp not null,
        username varchar(255) not null,
        primary key (id)
    )
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint UKaydweb1re2g5786xaugww4u0 unique (project_id, user_id)
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table users 
       add constraint UK_r43af9ap4edm43mmtq01oddj6 unique (username)
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKs97j8x0mru4m68ib34ysml3la 
       foreign key (approval_id) 
       references approvals
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKavkl7v9l6yfc9hhpaqi1q6f5u 
       foreign key (project_id) 
       references projects
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FKtoru9k6tjr6ifdjhood8onue8 
       foreign key (task_id) 
       references tasks
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table activity_logs 
       add constraint FK5bm1lt4f4eevt8lv2517soakd 
       foreign key (user_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKkh8kt9y5pin7d4qxs8i7pak8d 
       foreign key (approver_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKcda2y7tpb22ft0e1b4vfuqvl8 
       foreign key (project_id) 
       references projects
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK26bpb2a3url91ua6wpolo17v0 
       foreign key (requester_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FKmtvsmm2h8wgg44389ldqw955o 
       foreign key (reviewer_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table approvals 
       add constraint FK3s667b3xtqjakn4gpqwcyoibe 
       foreign key (task_id) 
       references tasks
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKdki1sp2homqsdcvqm9yrix31g 
       foreign key (project_id) 
       references projects
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table project_members 
       add constraint FKgul2el0qjk5lsvig3wgajwm77 
       foreign key (user_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table projects 
       add constraint FK14mww7skdu5cpg6nq2kwcnx0e 
       foreign key (creator_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKekr1dgiqktpyoip3qmp6lxsit 
       foreign key (assignee_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKt1ph5sat39g9lpa4g5kl46tbv 
       foreign key (creator_id) 
       references users
2025-07-24 12:29:02 [main] DEBUG org.hibernate.SQL - 
    
    alter table tasks 
       add constraint FKsfhn82y57i3k9uxww1s007acc 
       foreign key (project_id) 
       references projects
2025-07-24 12:29:02 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 12:29:02 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 12:29:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@600b3bee, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@129348e8, org.springframework.security.web.context.SecurityContextPersistenceFilter@9771c71, org.springframework.security.web.header.HeaderWriterFilter@7d836c4a, org.springframework.web.filter.CorsFilter@20ad64c, org.springframework.security.web.authentication.logout.LogoutFilter@2566247d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@8cce9ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63541cd4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@39a03ccc, org.springframework.security.web.session.SessionManagementFilter@6ba02f70, org.springframework.security.web.access.ExceptionTranslationFilter@6859bbd4, org.springframework.security.web.access.intercept.AuthorizationFilter@7b9b6a56]
2025-07-24 12:29:02 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-24 12:29:03 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-24 12:29:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 12:29:03 [main] INFO  c.e.p.ProjectManagementApplication - Started ProjectManagementApplication in 7.581 seconds (JVM running for 7.758)
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        User x */ select
            count(*) as col_0_0_ 
        from
            users user0_
2025-07-24 12:29:03 [main] INFO  c.e.p.config.DataInitializer - 开始初始化测试数据...
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:29:03.348337]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [系统管理员]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$uG2.V.lYc7rtcwxg9dnDReRiQ2u1k5k2Aa9qYB2JRxOhidZpv6Qba]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [ADMIN]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:29:03.348368]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [admin]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:29:03.447358]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [项目经理]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$IMRJpMmSbXPK990t2vgXHe6KEuWzMivgt0pmR7ougGvigH6scV.0K]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MANAGER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:29:03.447372]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [manager]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:29:03.539353]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发人员1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$F62hda8Cte8Zzr8to8URQewuOJMqhcRy11kKf6iEh9OrcKUhgx3Ri]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:29:03.539369]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [user1]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:29:03.631255]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发人员2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$8s5JKH.WZ4q7BX2eW7.PFetW/ku/GaE9KLehNy1yK.BrMLhlox2oK]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T12:29:03.631269]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [user2]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.633689]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [开发一个现代化的电商平台]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [电商平台开发]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:29:03.633697]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.635379]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [开发配套的移动应用]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [移动应用开发]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:29:03.635386]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Project
        */ insert 
        into
            projects
            (id, archived, createdAt, creator_id, description, endDate, name, startDate, status, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.636656]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [构建数据分析和报告系统]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [DATE] - [2026-01-24]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [数据分析系统]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-07-24]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [ACTIVE]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [TIMESTAMP] - [2025-07-24T12:29:03.636663]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.638472]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MANAGER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:29:03.638480]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.639742]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:29:03.639750]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.640524]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:29:03.640530]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [4]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.641565]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:29:03.641576]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.642897]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:29:03.642905]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [4]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ProjectMember
        */ insert 
        into
            project_members
            (id, active, joinedAt, project_id, role, updatedAt, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-24T12:29:03.643741]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [MEMBER]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-07-24T12:29:03.643747]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:29:03.646216]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [实现用户注册和验证功能]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [8]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [HIGH]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [用户注册功能]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:29:03.646229]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [4]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:29:03.648811]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [4]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [开发商品增删改查功能]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [8]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [MEDIUM]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [商品管理模块]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:29:03.648821]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:29:03.650311]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [集成第三方支付系统]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [8]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [HIGH]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [1]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [支付集成]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:29:03.650318]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [4]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:29:03.651576]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [4]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [设计移动应用界面]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [8]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [MEDIUM]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [UI设计]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:29:03.651583]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:29:03.652587]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [对接后端API接口]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [8]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [HIGH]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [2]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [API对接]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:29:03.652598]
2025-07-24 12:29:03 [main] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.Task
        */ insert 
        into
            tasks
            (id, actualHours, archived, assignee_id, createdAt, creator_id, description, dueDate, estimatedHours, priority, project_id, status, title, updatedAt) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [INTEGER] - [null]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BOOLEAN] - [false]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [TIMESTAMP] - [2025-07-24T12:29:03.654015]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [设计数据分析模型]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [DATE] - [2025-08-07]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [8]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [LOW]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [BIGINT] - [3]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [VARCHAR] - [TODO]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [数据建模]
2025-07-24 12:29:03 [main] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [13] as [TIMESTAMP] - [2025-07-24T12:29:03.654025]
2025-07-24 12:29:03 [main] INFO  c.e.p.config.DataInitializer - 测试数据初始化完成！
2025-07-24 12:29:49 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 12:29:49 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 12:29:49 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as id1_5_,
            user0_.active as active2_5_,
            user0_.avatar as avatar3_5_,
            user0_.createdAt as createda4_5_,
            user0_.department as departme5_5_,
            user0_.email as email6_5_,
            user0_.fullName as fullname7_5_,
            user0_.password as password8_5_,
            user0_.phone as phone9_5_,
            user0_.position as positio10_5_,
            user0_.role as role11_5_,
            user0_.updatedAt as updated12_5_,
            user0_.username as usernam13_5_ 
        from
            users user0_ 
        where
            user0_.username=?
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [admin]
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_LOGIN]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:29:49.406035]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 admin 登录成功]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [127.0.0.1]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 12:29:49 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [1]
2025-07-24 12:29:49 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as id1_5_,
            user0_.active as active2_5_,
            user0_.avatar as avatar3_5_,
            user0_.createdAt as createda4_5_,
            user0_.department as departme5_5_,
            user0_.email as email6_5_,
            user0_.fullName as fullname7_5_,
            user0_.password as password8_5_,
            user0_.phone as phone9_5_,
            user0_.position as positio10_5_,
            user0_.role as role11_5_,
            user0_.updatedAt as updated12_5_,
            user0_.username as usernam13_5_ 
        from
            users user0_ 
        where
            user0_.username=?
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [manager]
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_LOGIN]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T12:30:00.613801]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 manager 登录成功]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [127.0.0.1]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 12:30:00 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [2]
2025-07-24 12:30:00 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:06:32 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m29s781ms).
2025-07-24 13:12:32 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m30s61ms).
2025-07-24 13:18:03 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m30s547ms).
2025-07-24 13:24:03 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m30s555ms).
2025-07-24 13:29:26 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m22s541ms).
2025-07-24 13:29:52 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/register
2025-07-24 13:29:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/register
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/register
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0.id 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as col_0_0_ 
        from
            users user0_ 
        where
            user0_.username=? limit ?
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [testuser1753334992641]
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0.id 
    from
        User as generatedAlias0 
    where
        generatedAlias0.email=:param0 */ select
            user0_.id as col_0_0_ 
        from
            users user0_ 
        where
            user0_.email=? limit ?
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [<EMAIL>]
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.User
        */ insert 
        into
            users
            (id, active, avatar, createdAt, department, email, fullName, password, phone, position, role, updatedAt, username) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BOOLEAN] - [true]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T13:29:52.854113]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [<EMAIL>]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [测试用户]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [$2a$10$Hn35qwOpdtpAG36pKpReZ.fxC4HjnZ59WuEdOBQVggTzw1Iw.TEyC]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [VARCHAR] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [10] as [VARCHAR] - [MEMBER]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [11] as [TIMESTAMP] - [2025-07-24T13:29:52.854169]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [12] as [VARCHAR] - [testuser1753334992641]
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_REGISTER]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T13:29:52.856503]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 testuser1753334992641 注册成功]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [0:0:0:0:0:0:0:1]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 13:29:52 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [5]
2025-07-24 13:29:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:53 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-07-24 13:29:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.username=:param0 */ select
            user0_.id as id1_5_,
            user0_.active as active2_5_,
            user0_.avatar as avatar3_5_,
            user0_.createdAt as createda4_5_,
            user0_.department as departme5_5_,
            user0_.email as email6_5_,
            user0_.fullName as fullname7_5_,
            user0_.password as password8_5_,
            user0_.phone as phone9_5_,
            user0_.position as positio10_5_,
            user0_.role as role11_5_,
            user0_.updatedAt as updated12_5_,
            user0_.username as usernam13_5_ 
        from
            users user0_ 
        where
            user0_.username=?
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [testuser1753334992641]
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* insert com.example.projectmanagement.entity.ActivityLog
        */ insert 
        into
            activity_logs
            (id, activityType, approval_id, createdAt, description, ipAddress, project_id, task_id, user_id) 
        values
            (default, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [USER_LOGIN]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [BIGINT] - [null]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [TIMESTAMP] - [2025-07-24T13:29:53.849265]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [用户 testuser1753334992641 登录成功]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [127.0.0.1]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [BIGINT] - [null]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [BIGINT] - [null]
2025-07-24 13:29:53 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [5]
2025-07-24 13:29:53 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:55 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/me
2025-07-24 13:29:55 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:55 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:55 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/auth/me
2025-07-24 13:29:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 13:29:55 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/auth/me
2025-07-24 13:29:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:56 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/test/projects
2025-07-24 13:29:56 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:56 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:56 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/projects
2025-07-24 13:29:56 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:56 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 13:29:56 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/projects
2025-07-24 13:29:56 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:58 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/test/project
2025-07-24 13:29:58 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:58 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:29:58 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/test/project
2025-07-24 13:29:58 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:29:58 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 13:29:58 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/test/project
2025-07-24 13:29:58 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:30:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /actuator/health
2025-07-24 13:30:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:30:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:30:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/test/status
2025-07-24 13:30:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-24 13:30:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 13:30:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/test/status
2025-07-24 13:30:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-24 13:59:21 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 13:59:21 [SpringApplicationShutdownHook] INFO  o.h.t.s.i.SchemaDropperImpl$DelayedDropActionImpl - HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2025-07-24 13:59:21 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists activity_logs CASCADE 
2025-07-24 13:59:21 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists approvals CASCADE 
2025-07-24 13:59:21 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists project_members CASCADE 
2025-07-24 13:59:21 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists projects CASCADE 
2025-07-24 13:59:21 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists tasks CASCADE 
2025-07-24 13:59:21 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    
    drop table if exists users CASCADE 
2025-07-24 13:59:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-24 13:59:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
