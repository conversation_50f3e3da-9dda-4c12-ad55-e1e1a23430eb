// 测试页面加载的简单脚本
const { chromium } = require('playwright');

async function testPageLoading() {
    console.log('🔍 测试页面加载...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        console.log(`[${type.toUpperCase()}] ${text}`);
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
        console.error('页面错误:', error.message);
    });
    
    try {
        console.log('访问主页...');
        await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
        
        // 等待一下看是否有错误
        await page.waitForTimeout(3000);
        
        // 检查是否有错误提示
        const errorDiv = await page.locator('div:has-text("应用初始化失败")').count();
        if (errorDiv > 0) {
            console.error('❌ 页面显示初始化失败错误');
            const errorText = await page.locator('div:has-text("应用初始化失败")').textContent();
            console.error('错误内容:', errorText);
        } else {
            console.log('✅ 页面加载正常，没有初始化错误');
        }
        
        // 截图
        await page.screenshot({ path: 'page-loading-test.png' });
        console.log('截图已保存: page-loading-test.png');
        
    } catch (error) {
        console.error('测试失败:', error);
    } finally {
        await browser.close();
    }
}

testPageLoading();
